# Safe OpenAPI Generation Script (PowerShell)
Write-Host "========================================" -ForegroundColor Green
Write-Host " Safe OpenAPI Generation Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

function Stop-JavaProcesses {
    Write-Host "Stopping Java processes..." -ForegroundColor Yellow
    Get-Process -Name "java" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "Stopping Java process (PID: $($_.Id))" -ForegroundColor Yellow
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
    Start-Sleep -Seconds 2
}

function Test-OpenApiFiles {
    $files = @(
        "target\generated-sources\openapi\openapi.yml",
        "docker\spl_control\openapi.yml"
    )
    
    foreach ($file in $files) {
        if (Test-Path $file) {
            Write-Host "✓ Found: $file" -ForegroundColor Green
        } else {
            Write-Host "✗ Missing: $file" -ForegroundColor Red
        }
    }
}

try {
    # Step 1: Clean up existing processes
    Write-Host "Step 1: Cleaning up any existing Java processes..." -ForegroundColor Cyan
    Stop-JavaProcesses

    # Step 2: Maven compile
    Write-Host "Step 2: Running Maven compile..." -ForegroundColor Cyan
    $compileResult = & mvn clean compile -DskipTests
    if ($LASTEXITCODE -ne 0) {
        throw "Maven compile failed with exit code: $LASTEXITCODE"
    }

    # Step 3: Integration test
    Write-Host "Step 3: Running integration test with OpenAPI generation..." -ForegroundColor Cyan
    $testResult = & mvn integration-test -Pgenerate-openapi -DskipTests
    $mavenExitCode = $LASTEXITCODE

    # Step 4: Wait and cleanup
    Write-Host "Step 4: Waiting for processes to finish..." -ForegroundColor Cyan
    Start-Sleep -Seconds 5
    Stop-JavaProcesses

    # Step 5: Verify files
    Write-Host "Step 5: Verifying generated files..." -ForegroundColor Cyan
    Test-OpenApiFiles

    if ($mavenExitCode -eq 0) {
        Write-Host "========================================" -ForegroundColor Green
        Write-Host " OpenAPI Generation Completed Successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
    } else {
        Write-Host "========================================" -ForegroundColor Red
        Write-Host " OpenAPI Generation Failed (Exit Code: $mavenExitCode)" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
    }

} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Final cleanup
    Write-Host "Final cleanup..." -ForegroundColor Yellow
    Stop-JavaProcesses
}

Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
