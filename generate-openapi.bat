@echo off
echo Generating OpenAPI YAML file...
echo.

echo Step 1: Compiling the project...
call mvn clean compile -q

echo Step 2: Starting application and generating OpenAPI spec...
call mvn integration-test -Pgenerate-openapi -q

echo.
if exist "target\generated-sources\openapi\openapi.yml" (
    echo ✓ OpenAPI YAML file generated successfully!
    echo Location: target\generated-sources\openapi\openapi.yml
    echo.
    echo You can also copy it to the root directory:
    copy "target\generated-sources\openapi\openapi.yml" "openapi.yml" >nul
    echo ✓ Copied to: openapi.yml
) else (
    echo ✗ Failed to generate OpenAPI YAML file.
    echo Make sure your application can start successfully.
)

echo.
pause
