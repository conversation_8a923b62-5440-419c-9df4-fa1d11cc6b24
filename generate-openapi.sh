#!/bin/bash

echo "Generating OpenAPI YAML file..."
echo

echo "Step 1: Compiling the project..."
mvn clean compile -q

echo "Step 2: Starting application and generating OpenAPI spec..."
mvn integration-test -Pgenerate-openapi -q

echo
if [ -f "target/generated-sources/openapi/openapi.yml" ]; then
    echo "✓ OpenAPI YAML file generated successfully!"
    echo "Location: target/generated-sources/openapi/openapi.yml"
    echo
    echo "Files automatically copied to:"
    if [ -f "docker/spl_control/openapi.yml" ]; then
        echo "✓ docker/spl_control/openapi.yml"
    fi
    echo
    echo "You can also copy it to the root directory:"
    cp "target/generated-sources/openapi/openapi.yml" "openapi.yml"
    echo "✓ Copied to: openapi.yml"
else
    echo "✗ Failed to generate OpenAPI YAML file."
    echo "Make sure your application can start successfully."
fi

echo
