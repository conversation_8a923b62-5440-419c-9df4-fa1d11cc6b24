2025-09-15 14:21:57.969  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 26032 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-15 14:21:57.972  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-15 14:21:58.081  INFO 26032 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-15 14:21:58.082  INFO 26032 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-15 14:22:05.783  INFO 26032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15 14:22:06.333  INFO 26032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 526 ms. Found 15 JPA repository interfaces.
2025-09-15 14:22:08.692  INFO 26032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-15 14:22:08.704  INFO 26032 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-15 14:22:08.704  INFO 26032 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-15 14:22:08.896  INFO 26032 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-15 14:22:08.897  INFO 26032 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 10813 ms
2025-09-15 14:22:09.480  INFO 26032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-15 14:22:09.788  INFO 26032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-15 14:22:10.138  INFO 26032 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-15 14:22:10.390  INFO 26032 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-15 14:22:10.619  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-15 14:22:10.634  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-15 14:22:10.638  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-15 14:22:10.642  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-15 14:22:10.648  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-15 14:22:10.660  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-15 14:22:10.664  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-15 14:22:10.668  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-15 14:22:10.680  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-15 14:22:10.684  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-15 14:22:10.695  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-15 14:22:10.705  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-15 14:22:10.708  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-15 14:22:10.720  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-15 14:22:10.733  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-15 14:22:10.737  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-15 14:22:10.747  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-15 14:22:10.751  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-15 14:22:10.762  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-15 14:22:10.765  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-15 14:22:10.776  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-15 14:22:10.781  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-15 14:22:10.786  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-15 14:22:10.798  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-15 14:22:10.802  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-15 14:22:10.805  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-15 14:22:10.817  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-15 14:22:10.829  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-15 14:22:10.834  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-15 14:22:10.852  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-15 14:22:10.913  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-15 14:22:10.927  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-15 14:22:10.930  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-15 14:22:10.944  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-15 14:22:10.957  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-15 14:22:10.969  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-15 14:22:10.972  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-15 14:22:10.975  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-15 14:22:10.987  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-15 14:22:10.990  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-15 14:22:11.001  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-15 14:22:11.012  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-15 14:22:11.023  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-15 14:22:11.036  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-15 14:22:11.040  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-15 14:22:11.044  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-15 14:22:11.050  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-15 14:22:11.056  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-15 14:22:11.062  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-15 14:22:11.075  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-15 14:22:11.079  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-15 14:22:11.083  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-15 14:22:11.097  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-15 14:22:11.102  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-15 14:22:11.106  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-15 14:22:11.112  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-15 14:22:11.123  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-15 14:22:11.306  INFO 26032 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-15 14:22:11.492  INFO 26032 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-15 14:22:11.618  INFO 26032 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-15 14:22:11.722  INFO 26032 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-15 14:22:12.011  INFO 26032 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-15 14:22:12.356  INFO 26032 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-15 14:22:12.969  INFO 26032 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-15 14:22:12.977  INFO 26032 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-15 14:22:15.101  INFO 26032 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-15 14:22:15.133  INFO 26032 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-15 14:22:16.566  INFO 26032 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 176 ms
2025-09-15 14:22:18.361  INFO 26032 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1154 ms
2025-09-15 14:22:18.566  INFO 26032 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-15 14:22:18.676  INFO 26032 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 69 ms.
2025-09-15 14:22:19.025  INFO 26032 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-15 14:22:19.027  INFO 26032 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-15 14:22:19.206  INFO 26032 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 179 ms.
2025-09-15 14:22:21.284  WARN 26032 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-15 14:22:21.286  WARN 26032 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-15 14:22:21.703  INFO 26032 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@3e6d7370}
2025-09-15 14:22:23.476  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@dc75c686, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@cc5f56df, org.springframework.security.web.context.SecurityContextPersistenceFilter@dad6bde5, org.springframework.security.web.header.HeaderWriterFilter@d322c240, org.springframework.security.web.authentication.logout.LogoutFilter@b324c2d6, com.deb.spl.control.authorization.AuthenticationFilter@e7377117, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@af37fdbb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b1073ec, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@460e3228, org.springframework.security.web.session.SessionManagementFilter@e5e292dc, org.springframework.security.web.access.ExceptionTranslationFilter@168ee54d, org.springframework.security.web.access.intercept.AuthorizationFilter@6c046e25]
2025-09-15 14:22:23.487  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.488  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-15 14:22:23.490  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.490  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-15 14:22:23.490  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-15 14:22:23.491  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-15 14:22:23.491  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-15 14:22:23.491  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-15 14:22:23.491  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-15 14:22:23.491  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-15 14:22:23.491  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.491  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-15 14:22:23.492  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.492  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-15 14:22:23.492  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.492  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-15 14:22:23.492  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.492  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-15 14:22:23.492  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.492  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-15 14:22:23.492  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.492  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-15 14:22:23.492  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.492  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-15 14:22:23.494  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.494  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-15 14:22:23.494  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.494  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-15 14:22:23.494  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.494  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-15 14:22:23.494  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.494  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-15 14:22:23.495  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.495  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-15 14:22:23.495  WARN 26032 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-15 14:22:23.495  INFO 26032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-15 14:22:23.933  WARN 26032 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-15 14:22:24.892  INFO 26032 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-15 14:22:25.162  INFO 26032 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-15 14:22:25.248  INFO 26032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-15 14:22:25.278  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 28.018 seconds (JVM running for 29.764)
2025-09-15 14:22:25.299  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-15 14:22:25.300  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-15 14:22:25.300  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-15 14:22:25.300  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" "-javaagent:D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar=10629" -Dfile.encoding=UTF-8 -classpath D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar com.deb.spl.control.Application
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : EFC_10732_3789132940=1
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-15 14:22:25.301  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : IJ_RESTARTER_LOG=C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\log\restarter.log
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : EFC_10732_1262719628=1
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-15 14:22:25.302  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : EFC_10732_1592913036=1
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-15 14:22:25.304  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : EFC_10732_2283032206=1
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : EFC_10732_2775293581=1
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-15 14:22:25.305  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-15 14:22:25.306  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-15 14:22:25.306  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-15 14:22:25.306  INFO 26032 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-15 14:22:25.320  INFO 26032 --- [ForkJoinPool.commonPool-worker-2] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-15 14:22:25.364  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:25.364  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:22:26.812 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:22:26.819 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:22:26.828 ERROR 26032 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-15T14:22:26.828205
2025-09-15 14:22:26.833  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:26.839 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:27.553  INFO 26032 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-15 14:22:27.553  INFO 26032 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-15 14:22:27.559  INFO 26032 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-09-15 14:22:28.000  WARN 26032 --- [http-nio-8079-exec-1] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [280] milliseconds.
2025-09-15 14:22:28.225  INFO 26032 --- [http-nio-8079-exec-1] c.vaadin.flow.spring.SpringInstantiator  : The number of beans implementing 'I18NProvider' is 0. Cannot use Spring beans for I18N, falling back to the default behavior
2025-09-15 14:22:28.879  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:28.893 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:22:30.739 ERROR 26032 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-15T14:22:30.739983
2025-09-15 14:22:31.758  INFO 26032 --- [ForkJoinPool.commonPool-worker-2] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-15 14:22:31.844  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:31.851 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:33.277 ERROR 26032 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-15T14:22:33.277543800
2025-09-15 14:22:35.716 ERROR 26032 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-15T14:22:35.716766800
2025-09-15 14:22:36.964  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:36.984 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:42.335  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:42.348 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:43.616  INFO 26032 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-15 14:22:43.617  INFO 26032 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 11725 ms
2025-09-15 14:22:43.618  INFO 26032 --- [ForkJoinPool.commonPool-worker-2] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 18298ms
2025-09-15 14:22:43.618  INFO 26032 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-15 14:22:43.618  INFO 26032 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:10910/VAADIN/
2025-09-15 14:22:45.925  INFO 26032 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-15 14:22:45.925  INFO 26032 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-15 14:22:47.645  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:47.658 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:47.794 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:47.795 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:22:52.650  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:52.653 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:22:55.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:22:57.658 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:22:57.660  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:22:57.682 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:23:11.245 ERROR 26032 --- [http-nio-8079-exec-4] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:23:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:23:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:23:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:23:27.511  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:23:27.529 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:23:27.531 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:23:29.766  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:23:29.774 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:23:34.561 ERROR 26032 --- [http-nio-8079-exec-9] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:23:46.332 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:23:46.332 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:23:55.270  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:23:59.310 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:23:59.310  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:23:59.313 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:24:16.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:24:16.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:24:25.984  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:24:27.725 ERROR 26032 --- [http-nio-8079-exec-5] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:24:28.219  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:24:28.225 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:24:28.225 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:24:30.359  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:24:30.362 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:24:47.032 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:24:47.032 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:24:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:24:59.421 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:24:59.422  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:24:59.424 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:25:16.319 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:25:16.319 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:25:25.336  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:25:29.152  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:25:29.156 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:25:29.156 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:25:31.263  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:25:31.266 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:25:46.536 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:25:46.536 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:25:53.641  INFO 26032 --- [http-nio-8079-exec-8] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 7525 ms
2025-09-15 14:25:55.316  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:25:59.515 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:25:59.516  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:25:59.524 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:26:16.353 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:26:16.354 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:26:25.392  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:26:29.422  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:26:29.425 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:26:29.425 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:26:31.560  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:26:31.563 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:26:46.480 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:26:46.480 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:26:55.272  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:26:59.611 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:26:59.611  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:26:59.613 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:27:16.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:27:16.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:27:23.460  WARN 26032 --- [http-nio-8079-exec-8] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springdoc.api.OpenApiResourceNotFoundException: No OpenAPI resource found for group: api]
2025-09-15 14:27:25.967  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:27:30.258  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:27:30.262 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:27:30.263 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:27:32.372  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:27:32.374 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:27:47.001 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:27:47.001 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:27:53.155  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202; 	 requestQueryString null; 	 payload 
2025-09-15 14:27:53.212  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1000, createdAt=2025-09-15T14:27:53.164896900, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:27:53.224 ERROR 26032 --- [http-nio-8079-exec-7] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Unable to handle the Spring Security Exception because the response is already committed.] with root cause

org.springframework.security.access.AccessDeniedException: Access Denied
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:94) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at com.deb.spl.control.authorization.AuthenticationFilter.doFilterInternal(AuthenticationFilter.java:86) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at com.deb.spl.control.utils.logs.RequestCachingFilter.doFilterInternal(RequestCachingFilter.java:112) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at java.base/java.lang.Thread.run(Thread.java:858) ~[na:na]

2025-09-15 14:27:53.230  WARN 26032 --- [http-nio-8079-exec-7] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'application/json;charset=ISO-8859-1']
2025-09-15 14:27:55.020  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202; 	 requestQueryString null; 	 payload 
2025-09-15 14:27:55.030  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1001, createdAt=2025-09-15T14:27:55.023157900, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:27:55.033 ERROR 26032 --- [http-nio-8079-exec-10] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Unable to handle the Spring Security Exception because the response is already committed.] with root cause

org.springframework.security.access.AccessDeniedException: Access Denied
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:94) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at com.deb.spl.control.authorization.AuthenticationFilter.doFilterInternal(AuthenticationFilter.java:86) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at com.deb.spl.control.utils.logs.RequestCachingFilter.doFilterInternal(RequestCachingFilter.java:112) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at java.base/java.lang.Thread.run(Thread.java:858) ~[na:na]

2025-09-15 14:27:55.041  WARN 26032 --- [http-nio-8079-exec-10] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'application/json;charset=ISO-8859-1']
2025-09-15 14:27:55.310  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:27:59.784 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:27:59.784  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:27:59.787 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:28:16.363 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:28:16.363 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:28:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:28:30.696  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:28:30.699 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:28:30.699 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:28:32.812  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:28:32.815 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:28:46.363 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:28:46.364 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:28:55.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:28:59.972 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:28:59.972  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:28:59.975 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:29:16.342 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:29:16.343 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:29:25.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:29:31.547  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:29:31.549 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:29:31.549 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:29:33.663  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:29:33.666 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:29:46.333 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:29:46.333 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:29:55.299  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:30:00.106 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:30:00.107  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:30:00.110 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:30:16.351 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:30:16.352 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:30:25.292  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:30:31.611  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:30:31.614 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:30:31.615 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:30:33.713  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:30:33.716 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:30:46.344 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:30:46.344 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:30:55.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:31:00.196 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:31:00.196  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:31:00.199 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:31:16.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:31:16.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:31:25.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:31:31.722  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:31:31.724 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:31:31.725 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:31:33.840  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:31:33.843 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:31:46.347 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:31:46.347 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:31:55.386  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:32:00.376 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:32:00.376  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:32:00.380 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:32:16.431 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:32:16.431 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:32:25.286  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:32:32.828  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:32:32.831 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:32:32.831 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:32:34.934  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:32:34.936 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:32:46.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:32:46.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:32:55.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:33:00.545 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:33:00.545  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:33:00.548 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:33:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:33:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:33:25.283  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:33:32.943  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:33:32.946 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:33:32.946 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:33:35.056  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:33:35.059 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:33:46.332 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:33:46.333 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:33:55.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:34:00.936 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:34:00.937  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:34:00.939 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:34:11.565  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202; 	 requestQueryString null; 	 payload 
2025-09-15 14:34:11.585  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1002, createdAt=2025-09-15T14:34:11.569241900, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:34:11.590 ERROR 26032 --- [http-nio-8079-exec-8] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Unable to handle the Spring Security Exception because the response is already committed.] with root cause

org.springframework.security.access.AccessDeniedException: Access Denied
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:94) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at com.deb.spl.control.authorization.AuthenticationFilter.doFilterInternal(AuthenticationFilter.java:86) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at com.deb.spl.control.utils.logs.RequestCachingFilter.doFilterInternal(RequestCachingFilter.java:112) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at java.base/java.lang.Thread.run(Thread.java:858) ~[na:na]

2025-09-15 14:34:11.593  WARN 26032 --- [http-nio-8079-exec-8] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'application/json;charset=ISO-8859-1']
2025-09-15 14:34:16.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:34:16.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:34:20.904  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202; 	 requestQueryString null; 	 payload 
2025-09-15 14:34:20.912  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1003, createdAt=2025-09-15T14:34:20.906639700, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/%D0%91%D0%93%20%E2%84%96%202', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:34:20.915 ERROR 26032 --- [http-nio-8079-exec-8] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Unable to handle the Spring Security Exception because the response is already committed.] with root cause

org.springframework.security.access.AccessDeniedException: Access Denied
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:94) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at com.deb.spl.control.authorization.AuthenticationFilter.doFilterInternal(AuthenticationFilter.java:86) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.6.jar:5.7.6]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.25.jar:5.3.25]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at com.deb.spl.control.utils.logs.RequestCachingFilter.doFilterInternal(RequestCachingFilter.java:112) ~[classes/:na]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.25.jar:5.3.25]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at java.base/java.lang.Thread.run(Thread.java:858) ~[na:na]

2025-09-15 14:34:20.917  WARN 26032 --- [http-nio-8079-exec-8] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'application/json;charset=ISO-8859-1']
2025-09-15 14:34:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:34:33.288  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:34:33.291 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:34:33.291 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:34:35.402  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:34:35.404 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:34:46.310 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:34:46.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:34:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:35:01.267 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:35:01.268  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:35:01.270 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:35:16.331 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:35:16.332 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:35:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:35:33.315  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:35:33.317 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:35:33.318 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:35:35.427  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:35:35.431 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:35:46.336 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:35:46.336 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:35:55.284  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:36:01.389 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:36:01.389  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:36:01.393 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:36:16.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:36:16.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:36:25.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:36:33.441  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:36:33.444 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:36:33.444 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:36:35.552  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:36:35.555 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:36:46.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:36:46.323 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:36:46.515  INFO 26032 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/BG_3; 	 requestQueryString null; 	 payload 
2025-09-15 14:36:46.523  INFO 26032 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1004, createdAt=2025-09-15T14:36:46.516083, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/BG_3', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:36:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:37:00.948  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/BG_2; 	 requestQueryString null; 	 payload 
2025-09-15 14:37:00.964  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1005, createdAt=2025-09-15T14:37:00.951011300, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/BG_2', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:37:00.966  WARN 26032 --- [http-nio-8079-exec-4] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'com.deb.spl.control.data.asku.Readiness'; nested exception is org.springframework.core.convert.ConversionFailedException: Failed to convert from type [java.lang.String] to type [@org.springframework.web.bind.annotation.PathVariable com.deb.spl.control.data.asku.Readiness] for value 'BG_2'; nested exception is java.lang.IllegalArgumentException: No enum constant com.deb.spl.control.data.asku.Readiness.BG_2]
2025-09-15 14:37:01.523 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:37:01.524  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:37:01.527 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:37:05.578  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/BG_2A; 	 requestQueryString null; 	 payload 
2025-09-15 14:37:05.591  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1006, createdAt=2025-09-15T14:37:05.580237, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/BG_2A', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:37:13.075 ERROR 26032 --- [http-nio-8079-exec-10] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:37:16.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:37:16.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:37:16.552 ERROR 26032 --- [http-nio-8079-exec-7] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:37:18.016 ERROR 26032 --- [http-nio-8079-exec-5] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:37:18.949 ERROR 26032 --- [http-nio-8079-exec-10] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:37:19.810 ERROR 26032 --- [http-nio-8079-exec-5] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:37:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:37:34.044  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:37:34.047 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:37:34.048 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:37:36.282  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:37:36.289 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:37:46.359 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:37:46.359 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:37:55.270  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:38:01.756 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:38:01.757  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:38:01.759 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:38:16.305 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:38:16.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:38:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:38:34.840  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:38:34.843 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:38:34.845 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:38:37.094  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:38:37.100 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:38:46.420 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:38:46.420 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:38:55.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:39:01.872 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:39:01.872  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:39:01.874 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:39:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:39:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:39:20.576  INFO 26032 --- [http-nio-8079-exec-6] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/BG_2B; 	 requestQueryString null; 	 payload 
2025-09-15 14:39:20.584  INFO 26032 --- [http-nio-8079-exec-6] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1007, createdAt=2025-09-15T14:39:20.578521700, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/BG_2B', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:39:25.274  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:39:35.499  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:39:35.502 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:39:35.502 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:39:37.608  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:39:37.611 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:39:46.335 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:39:46.335 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:39:55.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:40:02.062 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:40:02.062  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:40:02.065 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:40:16.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:40:16.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:40:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:40:35.534  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:40:35.537 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:40:35.537 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:40:37.646  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:40:37.648 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:40:46.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:40:46.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:40:55.283  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:41:02.165 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:41:02.165  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:41:02.170 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:41:16.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:41:16.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:41:25.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:41:36.220  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:41:36.222 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:41:36.223 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:41:38.335  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:41:38.338 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:41:46.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:41:46.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:41:55.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:42:02.395 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:42:02.396  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:42:02.398 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:42:16.333 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:42:16.334 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:42:25.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:42:36.732  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:42:36.734 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:42:36.734 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:42:38.861  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:42:38.864 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:42:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:42:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:42:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:43:02.562 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:43:02.562  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:43:02.564 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:43:16.304 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:43:16.305 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:43:25.270  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:43:37.724  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:43:37.727 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:43:37.728 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:43:39.876  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:43:39.879 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:43:46.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:43:46.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:43:55.284  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:44:02.650 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:44:02.651  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:44:02.653 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:44:16.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:44:16.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:44:25.284  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:44:38.770  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:44:38.772 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:44:38.773 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:44:40.903  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:44:40.905 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:44:46.339 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:44:46.339 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:44:55.274  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:45:02.768 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:45:02.768  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:45:02.771 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:45:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:45:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:45:25.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:45:39.564  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:45:39.567 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:45:39.568 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:45:41.707  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:45:41.710 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:45:46.298 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:45:46.298 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:45:55.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:46:02.985 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:46:02.985  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:46:02.988 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:46:16.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:46:16.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:46:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:46:40.429  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:46:40.431 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:46:40.432 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:46:42.560  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:46:42.564 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:46:46.326 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:46:46.326 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:46:55.325  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:47:03.064 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:47:03.064  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:47:03.065 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:47:16.363 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:47:16.363 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:47:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:47:40.900  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:47:40.904 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:47:40.904 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:47:43.038  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:47:43.043 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:47:46.342 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:47:46.343 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:47:55.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:48:03.177 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:48:03.179  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:48:03.182 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:48:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:48:16.322 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:48:25.277  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:48:41.504  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:48:41.507 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:48:41.507 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:48:43.625  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:48:43.628 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:48:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:48:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:48:55.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:49:03.259 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:49:03.260  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:49:03.262 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:49:16.308 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:49:16.308 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:49:25.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:49:42.125  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:49:42.128 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:49:42.128 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:49:44.259  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:49:44.271 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:49:46.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:49:46.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:49:55.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:50:03.451 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:50:03.451  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:50:03.453 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:50:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:50:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:50:25.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:50:42.279  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:50:42.283 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:50:42.283 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:50:44.464  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:50:44.466 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:50:46.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:50:46.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:50:55.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:51:03.590 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:51:03.590  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:51:03.593 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:51:16.298 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:51:16.298 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:51:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:51:42.755  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:51:42.757 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:51:42.757 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:51:44.865  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:51:44.867 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:51:46.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:51:46.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:51:55.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:52:03.813 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:52:03.813  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:52:03.816 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:52:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:52:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:52:25.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:52:43.135  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:52:43.137 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:52:43.138 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:52:45.242  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:52:45.244 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:52:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:52:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:52:55.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:53:03.894 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:53:03.895  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:53:03.899 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:53:16.335 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:53:16.335 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:53:25.269  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:53:43.572  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:53:43.575 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:53:43.575 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:53:45.679  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:53:45.682 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:53:46.307 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:53:46.308 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:53:55.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:54:04.175 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:54:04.175  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:54:04.178 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:54:16.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:54:16.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:54:25.284  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:54:44.042  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:54:44.045 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:54:44.045 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:54:46.150  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:54:46.153 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:54:46.336 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:54:46.336 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:54:55.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:55:04.249 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:55:04.250  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:55:04.252 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:55:16.323 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:55:16.323 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:55:25.346  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:55:44.375  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:55:44.377 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:55:44.377 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:55:46.371 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:55:46.371 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:55:46.527  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:55:46.529 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:55:55.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:56:04.406 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:56:04.406  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:04.409 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:56:16.336 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:56:16.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:56:24.434  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/rocket; 	 requestQueryString isLeft=1; 	 payload {
    "initialData":  null,
    "initialDataSourceDescription":  "description",
    "storedTlKeys":  [
                         "123",
                         "123---sdfsdfsfd1465ssdfsd===="
                     ],
    "technicalCondition":  true,
    "initialDataTS":  null,
    "initialDataSource":  "MSG",
    "dateUseM":  null,
    "formData":  {
                     "gsnType":  "NO_GSN",
                     "isTelemetryIntegrated":  true,
                     "purposeType":  "COMBAT",
                     "alpType":  "NO_ALP",
                     "warhead":  "CBCH",
                     "plantMissile":  "11"
                 }
}
2025-09-15 14:56:24.443  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1008, createdAt=2025-09-15T14:56:24.435089700, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/rocket', cachedPayload='{
    "initialData":  null,
    "initialDataSourceDescription":  "description",
    "storedTlKeys":  [
                         "123",
                         "123---sdfsdfsfd1465ssdfsd===="
                     ],
    "technicalCondition":  true,
    "initialDataTS":  null,
    "initialDataSource":  "MSG",
    "dateUseM":  null,
    "formData":  {
                     "gsnType":  "NO_GSN",
                     "isTelemetryIntegrated":  true,
                     "purposeType":  "COMBAT",
                     "alpType":  "NO_ALP",
                     "warhead":  "CBCH",
                     "plantMissile":  "11"
                 }
}', requestQueryString='isLeft=1', headers=null}
2025-09-15 14:56:24.738  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/fire_order/11; 	 requestQueryString null; 	 payload {
    "launchInitialData":  {
                              "inclinationAngle":  -75,
                              "startTime":  "2025-07-05T18:30:35.8170186",
                              "scheduled":  "true",
                              "readiness":  "BG_3",
                              "tlCode":  "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
                              "longitudeRad":  "31.67633000",
                              "isProDetected":  true,
                              "missileOperatingMode":  "BASE_SNS",
                              "trajectory":  "AERO_BALLISTIC",
                              "loadTemperature":  -999.9,
                              "altitude":  0,
                              "latitudeRad":  "45.12877000",
                              "validatedByTlc":  true
                          },
    "orderInfo":  {
                      "entityId":  "aa29b624-0e2b-4687-a2e6-b268422cdb11",
                      "validUntil":  "2025-07-05T18:30:35.8170186"
                  }
}
2025-09-15 14:56:24.745  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1009, createdAt=2025-09-15T14:56:24.739550500, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/fire_order/11', cachedPayload='{
    "launchInitialData":  {
                              "inclinationAngle":  -75,
                              "startTime":  "2025-07-05T18:30:35.8170186",
                              "scheduled":  "true",
                              "readiness":  "BG_3",
                              "tlCode":  "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
                              "longitudeRad":  "31.67633000",
                              "isProDetected":  true,
                              "missileOperatingMode":  "BASE_SNS",
                              "trajectory":  "AERO_BALLISTIC",
                              "loadTemperature":  -999.9,
                              "altitude":  0,
                              "latitudeRad":  "45.12877000",
                              "validatedByTlc":  true
                          },
    "orderInfo":  {
                      "entityId":  "aa29b624-0e2b-4687-a2e6-b268422cdb11",
                      "validUntil":  "2025-07-05T18:30:35.8170186"
                  }
}', requestQueryString='null', headers=null}
2025-09-15 14:56:24.981  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/initial_data_with_ts; 	 requestQueryString isLeft=true; 	 payload 
2025-09-15 14:56:24.987  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1010, createdAt=2025-09-15T14:56:24.982015500, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/initial_data_with_ts', cachedPayload='', requestQueryString='isLeft=true', headers=null}
2025-09-15 14:56:25.076  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/initial_data_with_ts/commit; 	 requestQueryString isLeft=true&initial_data_id=1000; 	 payload {
    "loadTemperature":  -999.9,
    "latitudeRad":  "45.12877000",
    "longitudeRad":  "31.67633000",
    "altitude":  0.0,
    "inclinationAngle":  -75.0,
    "trajectory":  "AERO_BALLISTIC",
    "readiness":  "BG_3",
    "isProDetected":  true,
    "missileOperatingMode":  "BASE_SNS",
    "validatedByTlc":  true,
    "tlCode":  "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
    "scheduled":  true,
    "startTime":  "2025-07-05T18:30:35.8170186"
}
2025-09-15 14:56:25.084  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1011, createdAt=2025-09-15T14:56:25.076708100, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/initial_data_with_ts/commit', cachedPayload='{
    "loadTemperature":  -999.9,
    "latitudeRad":  "45.12877000",
    "longitudeRad":  "31.67633000",
    "altitude":  0.0,
    "inclinationAngle":  -75.0,
    "trajectory":  "AERO_BALLISTIC",
    "readiness":  "BG_3",
    "isProDetected":  true,
    "missileOperatingMode":  "BASE_SNS",
    "validatedByTlc":  true,
    "tlCode":  "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
    "scheduled":  true,
    "startTime":  "2025-07-05T18:30:35.8170186"
}', requestQueryString='isLeft=true&initial_data_id=1000', headers=null}
2025-09-15 14:56:25.179  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/byn; 	 requestQueryString null; 	 payload {
    "isBasuOtr1Connected":  true,
    "isF2":  false,
    "isF4":  false,
    "isNppaConnected":  true,
    "systemStatus":  "OK",
    "isBasuOtr2F3":  false,
    "isBuveF4":  false,
    "tvByn":  "OK",
    "isBasuOtr1F3":  false,
    "isBasuOtr2Connected":  false,
    "isConnected":  true,
    "isF3":  false,
    "isRgOutNcok":  false,
    "isBuveF2":  false,
    "operatingMode":  "COMBAT",
    "isNcok":  true,
    "isF1":  false,
    "isF5":  false
}
2025-09-15 14:56:25.186  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1012, createdAt=2025-09-15T14:56:25.179785900, direction=IN, adjacentSystem=BYN, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/byn', cachedPayload='{
    "isBasuOtr1Connected":  true,
    "isF2":  false,
    "isF4":  false,
    "isNppaConnected":  true,
    "systemStatus":  "OK",
    "isBasuOtr2F3":  false,
    "isBuveF4":  false,
    "tvByn":  "OK",
    "isBasuOtr1F3":  false,
    "isBasuOtr2Connected":  false,
    "isConnected":  true,
    "isF3":  false,
    "isRgOutNcok":  false,
    "isBuveF2":  false,
    "operatingMode":  "COMBAT",
    "isNcok":  true,
    "isF1":  false,
    "isF5":  false
}', requestQueryString='null', headers=null}
2025-09-15 14:56:25.246  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/ncok; 	 requestQueryString null; 	 payload {
    "isOtr1Lunched":  "UNDEFINED",
    "otr2AppPresence":  false,
    "tvNcok":  "OK",
    "systemStatus":  "OK",
    "appPresence":  false,
    "otr2TestResult":  "UNDEFINED",
    "otr1TestResult":  "OK",
    "otr1BinsInitialSetup":  "OK",
    "otr2BinsInitialSetup":  "UNDEFINED",
    "otr1tvNoIns":  "OK",
    "isSutoConnected":  true,
    "otr1BasSnsPz":  "OK",
    "nppaTestResult":  "UNDEFINED",
    "operatingMode":  "COMBAT",
    "otr2BasSnsPz":  "OK",
    "otrBinsPreciseSetup":  "OK",
    "otr2tvNoIns":  "OK",
    "isNcokConnected":  true,
    "isOtr2Lunched":  "UNDEFINED",
    "otr1AppPresence":  false
}
2025-09-15 14:56:25.252  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1013, createdAt=2025-09-15T14:56:25.246109800, direction=IN, adjacentSystem=NCOK, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/ncok', cachedPayload='{
    "isOtr1Lunched":  "UNDEFINED",
    "otr2AppPresence":  false,
    "tvNcok":  "OK",
    "systemStatus":  "OK",
    "appPresence":  false,
    "otr2TestResult":  "UNDEFINED",
    "otr1TestResult":  "OK",
    "otr1BinsInitialSetup":  "OK",
    "otr2BinsInitialSetup":  "UNDEFINED",
    "otr1tvNoIns":  "OK",
    "isSutoConnected":  true,
    "otr1BasSnsPz":  "OK",
    "nppaTestResult":  "UNDEFINED",
    "operatingMode":  "COMBAT",
    "otr2BasSnsPz":  "OK",
    "otrBinsPreciseSetup":  "OK",
    "otr2tvNoIns":  "OK",
    "isNcokConnected":  true,
    "isOtr2Lunched":  "UNDEFINED",
    "otr1AppPresence":  false
}', requestQueryString='null', headers=null}
2025-09-15 14:56:25.277  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:56:25.308  INFO 26032 --- [http-nio-8079-exec-6] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/rocket/sensors; 	 requestQueryString temperature=15.6&isLeft=true; 	 payload 
2025-09-15 14:56:25.314  INFO 26032 --- [http-nio-8079-exec-6] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1014, createdAt=2025-09-15T14:56:25.309777, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/rocket/sensors', cachedPayload='', requestQueryString='temperature=15.6&isLeft=true', headers=null}
2025-09-15 14:56:25.356  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/BG_3; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:25.365  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1015, createdAt=2025-09-15T14:56:25.358730700, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/BG_3', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:25.517  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:25.522  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1016, createdAt=2025-09-15T14:56:25.517878500, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:25.524  INFO 26032 --- [http-nio-8079-exec-10] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:25.525  WARN 26032 --- [http-nio-8079-exec-10] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:26.070  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:26.077  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1017, createdAt=2025-09-15T14:56:26.072250, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:26.078  INFO 26032 --- [http-nio-8079-exec-8] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:26.078  WARN 26032 --- [http-nio-8079-exec-8] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:26.593  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:26.610  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1018, createdAt=2025-09-15T14:56:26.594903900, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:26.611  INFO 26032 --- [http-nio-8079-exec-4] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:26.612  WARN 26032 --- [http-nio-8079-exec-4] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:27.119  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:27.126  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1019, createdAt=2025-09-15T14:56:27.119702300, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:27.127  INFO 26032 --- [http-nio-8079-exec-10] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:27.127  WARN 26032 --- [http-nio-8079-exec-10] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:27.647  INFO 26032 --- [http-nio-8079-exec-3] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:27.656  INFO 26032 --- [http-nio-8079-exec-3] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1020, createdAt=2025-09-15T14:56:27.650383900, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:27.657  INFO 26032 --- [http-nio-8079-exec-3] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:27.658  WARN 26032 --- [http-nio-8079-exec-3] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:28.165  INFO 26032 --- [http-nio-8079-exec-2] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:28.173  INFO 26032 --- [http-nio-8079-exec-2] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1021, createdAt=2025-09-15T14:56:28.167001700, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:28.174  INFO 26032 --- [http-nio-8079-exec-2] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:28.174  WARN 26032 --- [http-nio-8079-exec-2] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:28.680  INFO 26032 --- [http-nio-8079-exec-6] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:28.687  INFO 26032 --- [http-nio-8079-exec-6] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1022, createdAt=2025-09-15T14:56:28.681092200, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:28.687  INFO 26032 --- [http-nio-8079-exec-6] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:28.688  WARN 26032 --- [http-nio-8079-exec-6] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:29.194  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:29.208  INFO 26032 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1023, createdAt=2025-09-15T14:56:29.194850100, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:29.209  INFO 26032 --- [http-nio-8079-exec-4] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:29.210  WARN 26032 --- [http-nio-8079-exec-4] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:29.719  INFO 26032 --- [http-nio-8079-exec-9] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:29.730  INFO 26032 --- [http-nio-8079-exec-9] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1024, createdAt=2025-09-15T14:56:29.721394800, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:29.731  INFO 26032 --- [http-nio-8079-exec-9] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:29.731  WARN 26032 --- [http-nio-8079-exec-9] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:30.246  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:30.257  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1025, createdAt=2025-09-15T14:56:30.248761200, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:30.258  INFO 26032 --- [http-nio-8079-exec-8] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:30.259  WARN 26032 --- [http-nio-8079-exec-8] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:30.771  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:30.785  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1026, createdAt=2025-09-15T14:56:30.771887600, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:30.785  INFO 26032 --- [http-nio-8079-exec-5] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:30.786  WARN 26032 --- [http-nio-8079-exec-5] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:31.302  INFO 26032 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:31.309  INFO 26032 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1027, createdAt=2025-09-15T14:56:31.303095200, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:31.311  INFO 26032 --- [http-nio-8079-exec-1] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:31.311  WARN 26032 --- [http-nio-8079-exec-1] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:31.829  INFO 26032 --- [http-nio-8079-exec-2] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:31.837  INFO 26032 --- [http-nio-8079-exec-2] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1028, createdAt=2025-09-15T14:56:31.832231900, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:31.838  INFO 26032 --- [http-nio-8079-exec-2] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:31.839  WARN 26032 --- [http-nio-8079-exec-2] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:32.343  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 14:56:32.354  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1029, createdAt=2025-09-15T14:56:32.343853100, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:32.355  INFO 26032 --- [http-nio-8079-exec-7] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 14:56:32.355  WARN 26032 --- [http-nio-8079-exec-7] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 14:56:44.685  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:44.687 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:56:44.688 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:56:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:56:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:56:46.799  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:56:46.801 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:56:55.272  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:57:04.500 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:57:04.501  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:57:04.503 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:57:16.320 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:57:16.321 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:57:25.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:57:45.283  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:57:45.287 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:57:45.287 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:57:46.315 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:57:46.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:57:47.403  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:57:47.406 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:57:55.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:57:58.800  INFO 26032 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/fire_order/11; 	 requestQueryString null; 	 payload {
    "launchInitialData": {
        "loadTemperature": -999.9,
        "latitudeRad": "45.12877000",
        "longitudeRad": "31.67633000",
        "altitude": 0.0,
        "inclinationAngle": -75.0,
        "trajectory": "AERO_BALLISTIC",
        "readiness": "BG_3",
        "isProDetected": true,
        "missileOperatingMode": "BASE_SNS",
        "validatedByTlc": true,
        "tlCode": "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
        "scheduled": "true",
        "startTime": "2025-09-15T13:30:10.8170186" 
    },
    "orderInfo": {
        "entityId": "aa29b624-0e2b-4687-a2e6-b268422cdb11",
        "validUntil": "2026-07-05T18:30:35.8170186"
    }
}
2025-09-15 14:57:58.809  INFO 26032 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1030, createdAt=2025-09-15T14:57:58.803280400, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/fire_order/11', cachedPayload='{
    "launchInitialData": {
        "loadTemperature": -999.9,
        "latitudeRad": "45.12877000",
        "longitudeRad": "31.67633000",
        "altitude": 0.0,
        "inclinationAngle": -75.0,
        "trajectory": "AERO_BALLISTIC",
        "readiness": "BG_3",
        "isProDetected": true,
        "missileOperatingMode": "BASE_SNS",
        "validatedByTlc": true,
        "tlCode": "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
        "scheduled": "true",
        "startTime": "2025-09-15T13:30:10.8170186" 
    },
    "orderInfo": {
        "entityId": "aa29b624-0e2b-4687-a2e6-b268422cdb11",
        "validUntil": "2026-07-05T18:30:35.8170186"
    }
}', requestQueryString='null', headers=null}
2025-09-15 14:57:59.580 ERROR 26032 --- [pool-5-thread-1] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 14:58:04.559 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:58:04.559  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:58:04.561 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:58:12.632  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/fire_order/11; 	 requestQueryString null; 	 payload {
    "launchInitialData": {
        "loadTemperature": -999.9,
        "latitudeRad": "45.12877000",
        "longitudeRad": "31.67633000",
        "altitude": 0.0,
        "inclinationAngle": -75.0,
        "trajectory": "AERO_BALLISTIC",
        "readiness": "BG_2B",
        "isProDetected": true,
        "missileOperatingMode": "BASE_SNS",
        "validatedByTlc": true,
        "tlCode": "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
        "scheduled": "true",
        "startTime": "2025-09-15T13:30:10.8170186" 
    },
    "orderInfo": {
        "entityId": "aa29b624-0e2b-4687-a2e6-b268422cdb11",
        "validUntil": "2026-07-05T18:30:35.8170186"
    }
}
2025-09-15 14:58:12.648  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1031, createdAt=2025-09-15T14:58:12.632582300, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/fire_order/11', cachedPayload='{
    "launchInitialData": {
        "loadTemperature": -999.9,
        "latitudeRad": "45.12877000",
        "longitudeRad": "31.67633000",
        "altitude": 0.0,
        "inclinationAngle": -75.0,
        "trajectory": "AERO_BALLISTIC",
        "readiness": "BG_2B",
        "isProDetected": true,
        "missileOperatingMode": "BASE_SNS",
        "validatedByTlc": true,
        "tlCode": "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
        "scheduled": "true",
        "startTime": "2025-09-15T13:30:10.8170186" 
    },
    "orderInfo": {
        "entityId": "aa29b624-0e2b-4687-a2e6-b268422cdb11",
        "validUntil": "2026-07-05T18:30:35.8170186"
    }
}', requestQueryString='null', headers=null}
2025-09-15 14:58:16.333 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:58:16.333 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:58:25.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:58:45.925  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:58:45.928 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:58:45.929 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:58:46.331 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:58:46.331 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:58:47.995  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:58:47.997 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:58:55.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:59:04.702 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 14:59:04.703  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:59:04.706 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:59:16.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:59:16.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:59:25.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 14:59:46.314 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 14:59:46.315 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 14:59:46.513  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:59:46.516 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 14:59:46.517 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 14:59:48.624  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 14:59:48.627 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 14:59:55.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:00:04.821 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:00:04.821  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:00:04.824 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:00:16.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:00:16.326 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:00:25.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:00:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:00:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:00:46.909  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:00:46.912 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:00:46.912 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:00:49.058  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:00:49.061 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:00:55.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:01:04.971 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:01:04.972  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:01:04.974 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:01:06.577 ERROR 26032 --- [http-nio-8079-exec-7] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 15:01:15.183 ERROR 26032 --- [http-nio-8079-exec-6] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 15:01:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:01:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:01:25.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:01:35.757  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/fire_order/11; 	 requestQueryString null; 	 payload {
    "launchInitialData": {
        "loadTemperature": -999.9,
        "latitudeRad": "45.12877000",
        "longitudeRad": "31.67633000",
        "altitude": 0.0,
        "inclinationAngle": -75.0,
        "trajectory": "AERO_BALLISTIC",
        "readiness": "BG_2B",
        "isProDetected": true,
        "missileOperatingMode": "BASE_SNS",
        "validatedByTlc": true,
        "tlCode": "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
        "scheduled": "true",
        "startTime": "2025-09-15T15:30:10.8170186" 
    },
    "orderInfo": {
        "entityId": "aa29b624-0e2b-4687-a2e6-b268422cdb11",
        "validUntil": "2026-07-05T18:30:35.8170186"
    }
}
2025-09-15 15:01:35.764  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1032, createdAt=2025-09-15T15:01:35.758774, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/fire_order/11', cachedPayload='{
    "launchInitialData": {
        "loadTemperature": -999.9,
        "latitudeRad": "45.12877000",
        "longitudeRad": "31.67633000",
        "altitude": 0.0,
        "inclinationAngle": -75.0,
        "trajectory": "AERO_BALLISTIC",
        "readiness": "BG_2B",
        "isProDetected": true,
        "missileOperatingMode": "BASE_SNS",
        "validatedByTlc": true,
        "tlCode": "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
        "scheduled": "true",
        "startTime": "2025-09-15T15:30:10.8170186" 
    },
    "orderInfo": {
        "entityId": "aa29b624-0e2b-4687-a2e6-b268422cdb11",
        "validUntil": "2026-07-05T18:30:35.8170186"
    }
}', requestQueryString='null', headers=null}
2025-09-15 15:01:46.310 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:01:46.310 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:01:47.452  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:01:47.455 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:01:47.455 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:01:49.567  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:01:49.570 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:01:55.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:02:05.143 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:02:05.143  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:02:05.146 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:02:16.331 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:02:16.331 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:02:25.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:02:28.944  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/initial_data_with_ts; 	 requestQueryString isLeft=true; 	 payload 
2025-09-15 15:02:28.951  INFO 26032 --- [http-nio-8079-exec-10] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1033, createdAt=2025-09-15T15:02:28.946033400, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/initial_data_with_ts', cachedPayload='', requestQueryString='isLeft=true', headers=null}
2025-09-15 15:02:28.990  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/initial_data_with_ts/commit; 	 requestQueryString isLeft=true&initial_data_id=1003; 	 payload {
    "loadTemperature":  -999.9,
    "latitudeRad":  "45.12877000",
    "longitudeRad":  "31.67633000",
    "altitude":  0.0,
    "inclinationAngle":  -75.0,
    "trajectory":  "AERO_BALLISTIC",
    "readiness":  "BG_2B",
    "isProDetected":  true,
    "missileOperatingMode":  "BASE_SNS",
    "validatedByTlc":  true,
    "tlCode":  "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
    "scheduled":  true,
    "startTime":  "2025-09-15T15:30:10.8170186"
}
2025-09-15 15:02:28.996  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1034, createdAt=2025-09-15T15:02:28.990421100, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/initial_data_with_ts/commit', cachedPayload='{
    "loadTemperature":  -999.9,
    "latitudeRad":  "45.12877000",
    "longitudeRad":  "31.67633000",
    "altitude":  0.0,
    "inclinationAngle":  -75.0,
    "trajectory":  "AERO_BALLISTIC",
    "readiness":  "BG_2B",
    "isProDetected":  true,
    "missileOperatingMode":  "BASE_SNS",
    "validatedByTlc":  true,
    "tlCode":  "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==",
    "scheduled":  true,
    "startTime":  "2025-09-15T15:30:10.8170186"
}', requestQueryString='isLeft=true&initial_data_id=1003', headers=null}
2025-09-15 15:02:46.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:02:46.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:02:47.741  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:02:47.744 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:02:47.744 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:02:49.822  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:02:49.825 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:02:54.430 ERROR 26032 --- [http-nio-8079-exec-8] com.deb.spl.control.service.NppaService  : Attempt to send NPPA command declined NppaCommandDAO(usedInTestMode=false, usedInCombatMode=true, usedInWorkflow=false, hasBlocker=true, availableAtReadiness=[БГ № 2]). Command used in БГ № 3 while allowed in  БГ № 2
2025-09-15 15:02:54.432 ERROR 26032 --- [http-nio-8079-exec-8] c.d.s.control.views.automotion.PdpView   : Для використання команди перейдіть до  режиму - БГ № 2 class com.deb.spl.control.views.adjacentSystems.nppa.NppaView
2025-09-15 15:02:55.284  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:03:05.258 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:03:05.259  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:03:05.262 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:03:16.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:03:16.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:03:20.329  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/spl_readiness/BG_2B; 	 requestQueryString null; 	 payload 
2025-09-15 15:03:20.337  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1035, createdAt=2025-09-15T15:03:20.330829200, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/spl_readiness/BG_2B', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:03:25.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:03:25.413  INFO 26032 --- [http-nio-8079-exec-2] com.deb.spl.control.service.NppaService  : added Nppa command Command {command id 1000, command=' Otr1FromBg2bLaunch', originator , systemNCOK, generationTime= 2025-09-15T15:03:23.956442700, executingTime= null} at2025-09-15T15:03:23.956442700
2025-09-15 15:03:45.186  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/asku/fire_order; 	 requestQueryString isLeft=false; 	 payload 
2025-09-15 15:03:45.193  INFO 26032 --- [http-nio-8079-exec-5] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1036, createdAt=2025-09-15T15:03:45.188174500, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/asku/fire_order', cachedPayload='', requestQueryString='isLeft=false', headers=null}
2025-09-15 15:03:46.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:03:46.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:03:48.252  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:03:48.257 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:03:48.257 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:03:50.355  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:03:50.358 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:03:55.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:04:05.399 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:04:05.400  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:04:05.402 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:04:16.308 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:04:16.308 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:04:25.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:04:32.677  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 15:04:32.682  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1037, createdAt=2025-09-15T15:04:32.677920200, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:04:32.683  INFO 26032 --- [http-nio-8079-exec-7] com.deb.spl.control.service.NppaService  : commandValidityTime has expired
2025-09-15 15:04:32.683  WARN 26032 --- [http-nio-8079-exec-7] .w.s.m.a.ResponseStatusExceptionResolver : Resolved [com.deb.spl.control.controller.NotFoundException: commandValidityTime has expired]
2025-09-15 15:04:36.109  INFO 26032 --- [http-nio-8079-exec-4] com.deb.spl.control.service.NppaService  : added Nppa command Command {command id 1001, command=' Otr1FromBg2bLaunch', originator , systemNCOK, generationTime= 2025-09-15T15:04:34.863839200, executingTime= null} at2025-09-15T15:04:34.863839200
2025-09-15 15:04:37.779  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command; 	 requestQueryString null; 	 payload 
2025-09-15 15:04:37.794  INFO 26032 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1038, createdAt=2025-09-15T15:04:37.779682, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:04:46.027  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/nppa/command/1001/commit; 	 requestQueryString null; 	 payload 
2025-09-15 15:04:46.043  INFO 26032 --- [http-nio-8079-exec-8] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=1039, createdAt=2025-09-15T15:04:46.027967700, direction=IN, adjacentSystem=UNDEFINED, method='null', url='http://localhost:8079/api/v1/adjacent-systems/nppa/command/1001/commit', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:04:46.055  INFO 26032 --- [http-nio-8079-exec-8] com.deb.spl.control.service.NppaService  : command output locked during launch with command Command {command id 1001, command=' Otr1FromBg2bLaunch', originator , systemNCOK, generationTime= 2025-09-15T15:04:34.863839, executingTime= 2025-09-15T15:04:46.048895800}
2025-09-15 15:04:46.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:04:46.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:04:48.600  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:04:48.603 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:04:48.603 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:04:50.723  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:04:50.726 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:04:55.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:05:05.554 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:05:05.555  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:05:05.557 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:05:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:05:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:05:25.270  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:05:46.304 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:05:46.304 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:05:49.202  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:05:49.205 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:05:49.205 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:05:51.361  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:05:51.363 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:05:55.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:06:05.749 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:06:05.750  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:06:05.752 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:06:16.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:06:16.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:06:25.283  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:06:46.339 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:06:46.339 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:06:49.598  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:06:49.601 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:06:49.602 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:06:51.715  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:06:51.719 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:06:55.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:07:05.886 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:07:05.887  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:07:05.889 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:07:16.310 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:07:16.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:07:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:07:46.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:07:46.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:07:50.280  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:07:50.282 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:07:50.282 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:07:52.396  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:07:52.399 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:07:55.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:08:06.071 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:08:06.071  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:08:06.074 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:08:16.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:08:16.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:08:25.272  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:08:46.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:08:46.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:08:51.174  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:08:51.177 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:08:51.177 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:08:53.349  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:08:53.352 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:08:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:09:06.157 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:09:06.159  INFO 26032 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:09:06.181 ERROR 26032 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:09:16.341 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:09:16.341 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:09:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:09:46.319 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:09:46.319 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:09:51.309  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:09:51.311 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:09:51.312 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:09:53.421  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:09:53.424 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:09:55.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:10:06.437 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:10:06.437  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:10:06.440 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:10:16.342 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:10:16.342 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:10:25.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:10:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:10:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:10:52.305  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:10:52.308 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:10:52.308 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:10:54.414  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:10:54.417 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:10:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:11:06.557 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:11:06.558  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:11:06.561 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:11:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:11:16.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:11:25.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:11:46.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:11:46.325 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:11:52.494  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:11:52.496 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:11:52.496 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:11:54.613  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:11:54.616 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:11:55.281  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:12:06.732 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:12:06.732  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:12:06.733 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:12:16.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:12:16.312 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:12:25.275 ERROR 26032 --- [http-nio-8079-exec-10] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 15:12:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:12:46.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:12:46.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:12:53.123  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:12:53.126 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:12:53.126 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:12:55.250  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:12:55.252 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:12:55.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:13:06.924 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:13:06.924  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:13:06.927 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:13:16.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:13:16.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:13:25.311  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:13:46.349 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:13:46.350 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:13:53.586  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:13:53.588 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:13:53.588 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:13:55.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:13:55.656  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:13:55.658 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:14:07.015 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:14:07.015  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:14:07.018 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:14:16.315 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:14:16.315 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:14:25.272  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:14:46.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:14:46.309 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:14:53.728  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:14:53.731 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:14:53.731 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:14:55.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:14:55.859  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:14:55.863 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:15:07.536 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:15:07.537  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:15:07.539 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:15:16.321 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:15:16.321 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:15:25.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:15:46.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:15:46.306 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:15:54.070  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:15:54.072 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:15:54.072 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:15:55.285  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:15:56.176  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:15:56.179 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:16:07.729 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:16:07.730  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:16:07.732 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:16:16.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:16:16.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:16:25.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:16:46.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:16:46.327 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:16:54.467  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:16:54.469 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:16:54.469 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:16:55.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:16:56.604  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:16:56.607 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:17:07.826 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:17:07.826  INFO 26032 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:17:07.828 ERROR 26032 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:17:16.323 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:17:16.323 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:17:25.277  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:17:37.128 ERROR 26032 --- [http-nio-8079-exec-7] c.d.s.control.views.automotion.PdpView   : 
2025-09-15 15:17:46.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:17:46.319 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:17:54.851  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:17:54.853 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:17:54.854 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:17:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:17:56.968  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:17:56.970 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:18:07.928 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:18:07.928  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:18:07.944 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:18:16.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:18:16.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:18:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:18:46.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:18:46.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:18:55.264  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:18:55.266 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:18:55.267 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:18:55.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:18:57.384  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:18:57.390 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:19:08.005 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:19:08.005  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:19:08.008 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:19:16.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:19:16.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:19:25.270  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:19:46.335 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:19:46.335 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:19:55.316  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:19:55.319 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:19:55.319 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:19:55.320  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:19:57.429  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:19:57.432 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:20:08.118 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:20:08.118  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:20:08.121 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:20:16.373 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:20:16.373 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:20:25.277  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:20:46.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:20:46.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:20:55.386  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:20:55.389 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:20:55.390 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:20:55.390  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:20:57.535  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:20:57.538 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:21:08.516 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:21:08.516  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:21:08.518 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:21:16.418 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:21:16.418 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:21:25.280  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:21:46.341 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:21:46.341 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:21:55.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:21:55.744  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:21:55.746 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:21:55.746 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:21:57.846  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:21:57.849 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:22:08.655 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:22:08.655  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:22:08.672 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:22:16.338 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:22:16.338 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:22:25.272  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:22:46.307 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:22:46.307 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:22:55.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:22:56.034  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:22:56.036 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:22:56.037 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:22:58.156  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:22:58.158 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:23:08.821 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:23:08.821  INFO 26032 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:23:08.823 ERROR 26032 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:23:16.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:23:16.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:23:25.270  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:23:46.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:23:46.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:23:55.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:23:56.275  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:23:56.276 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:23:56.277 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:23:58.408  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:23:58.411 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:24:09.587 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:24:09.590  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:24:09.598 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:24:16.336 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:24:16.337 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:24:25.272  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:24:46.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:24:46.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:24:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:24:56.642  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:24:56.644 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:24:56.644 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:24:58.773  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:24:58.775 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:25:09.758 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:25:09.759  INFO 26032 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:25:09.761 ERROR 26032 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:25:16.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:25:16.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:25:25.274  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:25:46.301 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:25:46.301 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:25:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:25:56.850  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:25:56.852 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:25:56.852 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:25:59.034  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:25:59.037 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:26:09.920 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:26:09.921  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:26:09.925 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:26:16.328 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:26:16.329 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:26:25.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:26:46.305 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:26:46.305 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:26:55.275  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:26:57.714  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:26:57.717 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:26:57.717 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:26:59.826  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:26:59.829 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:27:10.046 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:27:10.046  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:27:10.048 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:27:16.314 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:27:16.314 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:27:25.269  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:27:46.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:27:46.311 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:27:55.273  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:27:57.992  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:27:57.995 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:27:57.995 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:28:00.113  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:28:00.117 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:28:10.137 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:28:10.137  INFO 26032 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:28:10.139 ERROR 26032 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:28:16.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:28:16.316 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:28:25.274  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:28:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:28:46.313 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:28:55.278  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:28:58.384  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:28:58.387 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:28:58.387 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:29:00.501  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:29:00.503 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:29:10.274 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:29:10.274  INFO 26032 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:29:10.276 ERROR 26032 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:29:16.321 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:29:16.321 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:29:25.276  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:29:46.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:29:46.330 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:29:55.347  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:29:58.496  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:29:58.497 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:29:58.497 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:30:00.614  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:30:00.617 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:30:10.498 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:30:10.498  INFO 26032 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:30:10.499 ERROR 26032 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:30:16.398 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:30:16.398 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:30:25.279  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:30:46.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:30:46.317 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:30:55.269  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:30:58.875  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:30:58.877 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:30:58.877 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:31:00.988  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:31:00.990 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:31:10.650 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:31:10.650  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:31:10.653 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:31:16.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:31:16.318 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:31:25.271  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:31:46.310 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:31:46.310 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:31:55.282  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:31:59.269  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:31:59.271 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-15 15:31:59.273 ERROR 26032 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-15 15:32:01.374  INFO 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:32:01.376 ERROR 26032 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-15 15:32:10.770 ERROR 26032 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-15 15:32:10.771  INFO 26032 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-15 15:32:10.773 ERROR 26032 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:32:16.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-15 15:32:16.324 ERROR 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-15 15:32:25.284  INFO 26032 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-15 15:32:32.193 ERROR 26032 --- [task-7] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-15 15:32:32.200  INFO 26032 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-15 15:32:32.219  INFO 26032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-15 15:32:32.231  INFO 26032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
