2025-08-29 18:35:27.253 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-08-29 18:35:27.264 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-08-29T18:35:27.244455100]
2025-08-29 18:35:27.265 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [3024]
2025-08-29 18:35:27.267 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.267 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-08-29 18:35:27.270 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.270 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-08-29 18:35:27.271 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.271 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-08-29 18:35:27.273 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.273 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-08-29 18:35:27.275 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.275 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-08-29 18:35:27.277 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.277 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-08-29 18:35:27.280 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.280 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-08-29 18:35:27.281 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.281 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-08-29 18:35:27.283 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.283 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-08-29 18:35:27.286 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.286 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-08-29 18:35:27.288 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.288 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-08-29 18:35:27.290 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.290 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-08-29 18:35:27.292 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.292 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-08-29 18:35:27.293 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.293 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-08-29 18:35:27.295 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.295 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-08-29 18:35:27.297 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.297 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-08-29 18:35:27.299 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.299 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-08-29 18:35:27.301 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.301 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-08-29 18:35:27.302 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.302 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-08-29 18:35:27.304 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.304 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-08-29 18:35:27.305 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.305 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-08-29 18:35:27.307 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.307 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-08-29 18:35:27.309 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.309 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-08-29 18:35:27.311 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.311 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-08-29 18:35:27.312 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.312 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-08-29 18:35:27.314 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.314 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-08-29 18:35:27.315 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.315 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-08-29 18:35:27.317 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.317 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-08-29 18:35:27.319 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.319 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-08-29 18:35:27.319 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.321 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-08-29 18:35:27.322 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.322 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-08-29 18:35:27.324 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.324 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-08-29 18:35:27.325 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.325 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-08-29 18:35:27.327 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.327 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-08-29 18:35:27.329 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.330 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-08-29 18:35:27.331 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.331 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-08-29 18:35:27.332 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.332 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-08-29 18:35:27.334 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.334 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-08-29 18:35:27.336 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.336 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-08-29 18:35:27.338 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.338 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-08-29 18:35:27.340 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.341 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-08-29 18:35:27.343 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.343 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-08-29 18:35:27.344 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.344 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-08-29 18:35:27.346 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.346 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-08-29 18:35:27.348 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.348 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-08-29 18:35:27.349 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.349 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-08-29 18:35:27.351 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.351 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-08-29 18:35:27.352 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.352 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-08-29 18:35:27.354 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.354 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-08-29 18:35:27.355 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.355 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-08-29 18:35:27.357 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.357 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-08-29 18:35:27.359 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.359 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-08-29 18:35:27.360 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.360 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-08-29 18:35:27.363 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.363 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-08-29 18:35:27.364 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.364 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-08-29 18:35:27.364 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.366 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-08-29 18:35:27.367 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.367 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-08-29 18:35:27.368 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.368 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-08-29 18:35:27.370 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.370 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-08-29 18:35:27.372 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.372 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-08-29 18:35:27.374 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.374 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-08-29 18:35:27.375 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.376 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-08-29 18:35:27.377 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.377 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-08-29 18:35:27.379 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.379 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-08-29 18:35:27.380 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.380 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-08-29 18:35:27.382 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.382 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-08-29 18:35:27.383 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.383 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-08-29 18:35:27.385 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.385 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-08-29 18:35:27.387 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3024]
2025-08-29 18:35:27.387 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-08-29 18:35:27.409 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.412 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:27.414 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.416 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-08-29 18:35:27.419  WARN 5592 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-08-29 18:35:27.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.421 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.424 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.427 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.429 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.431 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.433 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:27.436  WARN 5592 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-08-29 18:35:27.436 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:27.438 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-08-29 18:35:27.440 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:27.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-08-29 18:35:27.444 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-08-29 18:35:29.417 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-08-29 18:35:29.418 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-08-29 18:35:29.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-08-29 18:35:29.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-08-29 18:35:29.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-08-29 18:35:29.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-08-29 18:35:29.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-08-29 18:35:29.419 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-08-29 18:35:29.435 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-08-29 18:35:29.437 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-08-29 18:35:29.442 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-08-29 18:35:29.443 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-08-29T18:35:29.440540300]
2025-08-29 18:35:29.449 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.449 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.449 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-08-29T18:35:29.440540300]
2025-08-29 18:35:29.450 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [3025]
2025-08-29 18:35:29.453 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.453 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-08-29 18:35:29.455 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.455 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-08-29 18:35:29.457 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.457 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-08-29 18:35:29.459 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.460 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-08-29 18:35:29.462 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.462 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-08-29 18:35:29.464 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.464 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-08-29 18:35:29.467 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.467 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-08-29 18:35:29.468 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.468 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-08-29 18:35:29.470 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.470 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-08-29 18:35:29.472 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.472 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-08-29 18:35:29.473 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.473 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-08-29 18:35:29.476 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.476 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-08-29 18:35:29.478 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.478 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-08-29 18:35:29.480 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.480 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-08-29 18:35:29.482 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.482 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-08-29 18:35:29.483 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.483 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-08-29 18:35:29.485 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.485 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-08-29 18:35:29.487 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.487 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-08-29 18:35:29.489 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.489 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-08-29 18:35:29.491 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.491 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-08-29 18:35:29.494 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.494 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-08-29 18:35:29.496 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.497 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-08-29 18:35:29.498 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.498 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-08-29 18:35:29.500 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.500 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-08-29 18:35:29.502 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.502 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-08-29 18:35:29.503 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.503 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-08-29 18:35:29.505 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.505 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-08-29 18:35:29.507 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.507 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-08-29 18:35:29.522 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.523 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-08-29 18:35:29.526 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.526 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-08-29 18:35:29.612 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.612 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-08-29 18:35:29.627 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.628 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-08-29 18:35:29.629 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.629 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-08-29 18:35:29.631 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.631 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-08-29 18:35:29.634 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.634 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-08-29 18:35:29.639 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.641 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-08-29 18:35:29.643 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.643 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-08-29 18:35:29.645 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.645 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-08-29 18:35:29.650 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.650 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-08-29 18:35:29.652 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.652 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-08-29 18:35:29.654 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.654 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-08-29 18:35:29.656 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.656 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-08-29 18:35:29.661 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.661 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-08-29 18:35:29.673 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.673 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-08-29 18:35:29.685 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.685 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-08-29 18:35:29.688 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.688 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-08-29 18:35:29.692 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.692 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-08-29 18:35:29.699 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.699 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-08-29 18:35:29.702 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.702 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-08-29 18:35:29.704 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.704 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-08-29 18:35:29.707 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.707 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-08-29 18:35:29.711 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.711 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-08-29 18:35:29.714 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.714 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-08-29 18:35:29.719 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.719 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-08-29 18:35:29.723 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.723 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-08-29 18:35:29.724 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.726 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-08-29 18:35:29.729 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.729 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-08-29 18:35:29.735 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.735 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-08-29 18:35:29.738 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.738 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-08-29 18:35:29.741 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.742 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-08-29 18:35:29.745 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.745 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-08-29 18:35:29.746 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.747 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-08-29 18:35:29.749 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.750 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-08-29 18:35:29.753 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.753 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-08-29 18:35:29.763 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.763 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-08-29 18:35:29.765 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.766 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-08-29 18:35:29.769 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.769 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-08-29 18:35:29.772 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.772 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-08-29 18:35:29.774 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3025]
2025-08-29 18:35:29.774 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-08-29 18:35:29.816 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.820 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:29.823 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.828 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-08-29 18:35:29.837  WARN 5592 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-08-29 18:35:29.837 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.840 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.845 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.856 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.859 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.864 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.867 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-08-29 18:35:29.870  WARN 5592 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-08-29 18:35:29.871 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:29.876 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-08-29 18:35:29.886 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-08-29 18:35:29.895 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-08-29 18:35:29.900 TRACE 5592 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
