2025-09-13 15:16:15.646 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:15.654 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:15.654 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:15.654 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:15.654 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:15.658 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:15.657786200]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:15.657786200]
2025-09-13 15:16:15.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5499]
2025-09-13 15:16:15.666 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.666 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:15.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:15.670 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.670 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:15.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.672 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:15.673 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.673 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:15.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:15.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:15.678 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.678 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:15.680 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.680 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:15.681 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.681 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:15.683 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.683 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:15.685 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.685 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:15.686 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.686 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:15.688 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.688 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:15.689 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.689 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:15.691 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.691 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:15.693 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.693 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:15.694 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.694 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:15.696 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.696 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:15.698 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.698 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:15.699 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.699 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:15.700 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.700 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:15.707 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.707 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:15.716 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.716 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:15.718 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.718 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:15.719 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.719 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:15.721 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.721 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:15.722 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.722 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:15.724 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.724 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:15.726 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.726 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:15.727 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.727 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:15.729 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.729 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:15.731 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.731 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:15.732 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.732 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:15.734 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.734 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:15.735 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.735 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:15.737 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.737 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:15.739 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.739 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:15.740 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.741 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:15.742 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.742 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:15.744 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.744 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:15.746 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.746 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:15.747 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.747 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:15.748 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.749 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:15.751 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.751 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:15.752 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.752 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:15.754 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.754 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:15.755 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.755 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:15.756 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.756 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:15.758 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.758 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:15.758 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.758 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:15.760 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.761 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:15.762 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.762 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:15.764 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.764 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:15.766 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.766 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:15.767 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.767 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:15.769 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.769 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:15.771 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.771 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:15.772 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.772 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:15.774 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.774 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:15.775 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.775 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:15.777 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.777 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:15.778 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.779 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:15.780 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.780 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:15.781 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.781 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:15.783 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.783 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:15.785 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.785 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:15.786 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.786 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:15.787 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5499]
2025-09-13 15:16:15.787 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:15.800 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.802 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:15.804 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.805 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:15.807  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:15.808 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.809 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.810 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.813 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.816 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.818 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:15.819  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:15.820 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:15.821 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:15.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:15.824 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:15.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:17.814 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:17.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:17.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:17.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:17.825 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.825 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.825 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:17.826 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:17.825327900]
2025-09-13 15:16:17.829 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.829 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.829 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:17.825327900]
2025-09-13 15:16:17.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5500]
2025-09-13 15:16:17.833 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.833 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:17.835 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.835 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:17.836 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.836 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:17.838 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.838 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:17.839 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.839 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:17.840 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.841 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:17.842 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.842 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:17.844 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.844 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:17.845 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.845 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:17.847 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.847 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:17.848 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.848 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:17.849 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.849 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:17.851 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.851 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:17.853 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.853 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:17.854 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.854 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:17.856 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.856 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:17.857 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.857 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:17.859 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.859 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:17.860 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.861 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:17.862 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.862 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:17.863 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.863 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:17.864 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.864 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:17.865 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.865 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:17.867 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.867 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:17.869 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.869 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:17.870 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.870 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:17.872 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.872 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:17.874 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.874 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:17.874 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.874 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:17.877 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.877 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:17.878 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.878 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:17.880 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.880 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:17.881 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.882 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:17.883 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.883 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:17.884 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.884 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:17.886 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.886 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:17.888 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.888 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:17.889 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.889 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:17.890 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.891 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:17.892 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.892 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:17.894 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.894 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:17.895 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.895 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:17.897 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.897 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:17.898 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.898 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:17.899 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.899 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:17.901 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.901 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:17.902 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.902 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:17.904 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.904 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:17.905 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.905 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:17.906 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.906 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:17.907 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.907 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:17.909 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.909 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:17.911 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.911 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:17.912 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.912 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:17.914 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.914 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:17.915 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.915 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:17.916 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.916 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:17.919 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.919 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:17.920 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.920 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:17.921 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.921 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:17.923 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.923 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:17.925 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.925 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:17.926 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.926 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:17.927 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.927 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:17.928 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.928 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:17.930 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.930 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:17.931 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.932 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:17.933 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.933 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:17.934 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5500]
2025-09-13 15:16:17.934 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:17.948 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.950 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:17.951 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.954 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:17.955  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:17.955 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.966 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.972 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.976 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.980 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.981 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:17.983  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:17.983 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:17.984 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:17.986 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:17.988 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:17.989 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:19.957 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:19.958 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:19.977 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:19.977 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:19.978 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:19.982 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:19.983 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:19.983 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:19.982374500]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:19.982374500]
2025-09-13 15:16:19.987 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5501]
2025-09-13 15:16:19.989 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.989 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:19.991 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.991 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:19.992 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.992 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:19.994 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.994 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:19.996 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.996 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:19.997 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.997 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:19.998 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:19.999 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:20.000 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.000 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:20.002 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.002 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:20.003 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.003 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:20.005 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.005 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:20.006 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.006 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:20.008 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.008 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:20.009 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.009 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:20.010 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.011 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:20.012 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.012 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:20.014 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.014 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:20.015 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.015 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:20.016 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.016 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:20.018 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.018 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:20.019 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.019 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:20.021 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.021 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:20.022 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.022 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:20.024 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.024 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:20.025 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.025 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:20.027 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.027 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:20.027 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.028 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:20.028 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.028 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:20.031 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.031 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:20.032 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.032 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:20.033 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.033 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:20.036 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.036 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:20.037 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.037 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:20.037 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.037 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:20.040 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.040 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:20.041 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.041 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:20.042 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.042 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:20.044 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.044 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:20.046 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.046 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:20.047 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.047 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:20.048 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.049 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:20.050 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.050 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:20.051 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.052 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:20.053 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.053 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:20.054 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.054 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:20.056 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.056 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:20.057 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.058 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:20.059 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.059 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:20.060 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.060 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:20.061 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.061 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:20.063 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.063 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:20.065 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.065 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:20.066 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.066 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:20.068 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.068 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:20.070 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.070 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:20.071 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.071 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:20.072 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.072 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:20.074 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.074 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:20.075 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.075 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:20.077 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.077 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:20.078 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.078 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:20.079 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.079 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:20.081 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.081 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:20.082 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.082 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:20.084 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.084 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:20.085 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.086 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:20.087 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.087 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:20.088 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.088 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:20.089 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5501]
2025-09-13 15:16:20.089 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:20.103 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.105 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:20.107 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:20.110  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:20.110 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.112 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.114 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.117 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.120 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:20.122  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:20.122 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:20.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:20.126 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:20.127 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:20.128 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:22.107 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:22.107 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:22.108 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:22.109 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:22.115 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:22.116 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:22.119 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:22.119153700]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:22.119153700]
2025-09-13 15:16:22.124 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5502]
2025-09-13 15:16:22.126 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.126 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:22.128 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.128 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:22.130 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.130 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:22.131 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.131 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:22.133 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.133 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:22.134 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.134 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:22.136 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.136 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:22.137 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.137 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:22.139 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.139 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:22.140 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.140 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:22.141 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.141 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:22.143 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.143 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:22.145 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.145 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:22.146 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.146 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:22.147 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.147 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:22.149 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.149 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:22.150 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.150 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:22.152 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.152 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:22.154 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.154 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:22.155 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.155 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:22.156 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.156 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:22.158 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.158 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:22.159 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.159 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:22.161 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.161 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:22.162 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.162 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:22.163 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.163 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:22.165 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.165 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:22.167 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.167 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:22.168 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.168 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:22.169 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.169 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:22.171 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.171 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:22.172 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.172 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:22.174 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.174 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:22.175 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.175 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:22.175 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.175 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:22.176 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.176 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:22.178 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.178 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:22.179 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.179 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:22.181 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.181 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:22.182 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.182 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:22.183 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.183 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:22.184 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.185 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:22.186 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.186 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:22.187 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.187 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:22.189 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.189 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:22.190 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.191 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:22.192 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.192 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:22.193 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.193 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:22.194 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.195 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:22.196 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.196 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:22.197 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.197 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:22.198 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.198 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:22.200 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.200 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:22.201 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.201 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:22.202 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.203 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:22.204 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.204 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:22.205 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.205 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:22.207 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.207 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:22.208 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.208 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:22.209 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.209 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:22.211 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.211 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:22.212 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.212 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:22.214 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.214 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:22.215 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.215 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:22.216 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.217 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:22.218 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.218 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:22.219 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.219 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:22.220 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.220 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:22.222 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5502]
2025-09-13 15:16:22.222 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:22.240 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.246 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:22.248 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:22.250  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:22.251 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.252 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.254 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.256 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.259 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.260 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:22.262  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:22.262 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:22.263 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:22.265 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:22.266 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:22.268 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:24.241 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:24.242 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:24.243 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:24.249 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:24.250 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:24.253 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:24.254 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:24.254 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:24.254 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:24.254 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:24.253059300]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:24.257 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:24.253059300]
2025-09-13 15:16:24.258 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5503]
2025-09-13 15:16:24.260 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.260 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:24.262 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.262 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:24.263 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.263 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:24.265 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.265 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:24.267 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.267 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:24.268 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.268 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:24.269 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.269 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:24.271 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.271 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:24.272 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.272 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:24.274 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.274 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:24.276 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.276 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:24.277 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.277 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:24.278 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.278 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:24.279 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.279 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:24.281 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.281 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:24.283 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.283 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:24.284 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.284 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:24.285 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.285 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:24.287 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.287 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:24.288 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.288 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:24.290 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.290 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:24.291 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.291 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:24.293 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.293 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:24.294 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.295 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:24.296 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.296 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:24.297 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.297 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:24.299 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.299 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:24.300 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.300 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:24.301 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.301 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:24.303 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.303 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:24.304 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.304 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:24.306 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.306 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:24.307 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.307 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:24.309 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.309 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:24.310 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.311 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:24.312 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.312 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:24.313 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.313 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:24.315 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.315 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:24.316 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.316 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:24.318 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.318 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:24.319 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.319 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:24.320 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.320 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:24.322 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.322 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:24.323 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.323 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:24.325 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.325 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:24.326 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.326 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:24.327 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.327 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:24.329 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.329 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:24.331 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.331 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:24.333 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.333 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:24.334 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.334 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:24.335 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.335 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:24.337 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.337 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:24.338 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.338 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:24.339 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.339 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:24.340 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.340 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:24.342 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.342 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:24.344 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.344 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:24.345 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.345 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:24.347 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.347 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:24.348 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.348 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:24.350 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.350 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:24.351 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.351 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:24.353 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.353 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:24.354 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.355 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:24.356 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.356 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:24.357 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.357 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:24.359 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.359 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:24.360 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5503]
2025-09-13 15:16:24.360 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:24.372 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:24.376 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.377 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:24.378  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:24.379 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.381 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.383 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.389 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:24.392  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:24.392 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:24.394 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:24.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:24.398 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:24.399 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:26.373 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:26.374 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:26.375 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:26.386 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:26.387 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:26.390 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:26.391 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:26.390096300]
2025-09-13 15:16:26.395 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.395 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.395 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:26.390096300]
2025-09-13 15:16:26.396 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5504]
2025-09-13 15:16:26.398 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.398 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:26.399 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.399 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:26.400 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.400 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:26.402 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.402 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:26.403 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.403 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:26.405 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.405 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:26.406 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.406 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:26.407 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.407 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:26.409 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.409 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:26.410 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.410 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:26.411 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.411 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:26.413 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.413 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:26.414 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.414 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:26.416 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.416 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:26.417 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.417 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:26.419 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.419 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:26.420 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.420 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:26.422 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.422 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:26.423 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.423 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:26.424 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.424 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:26.426 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.426 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:26.427 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.427 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:26.428 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.428 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:26.429 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.429 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:26.431 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.431 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:26.432 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.432 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:26.434 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.434 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:26.435 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.435 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:26.436 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.436 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:26.436 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.436 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:26.439 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.439 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:26.440 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.440 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:26.441 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.441 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:26.443 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.443 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:26.444 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.444 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:26.446 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.446 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:26.447 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.447 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:26.448 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.448 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:26.450 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.450 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:26.451 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.451 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:26.452 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.452 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:26.454 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.454 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:26.455 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.455 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:26.456 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.456 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:26.457 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.457 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:26.459 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.459 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:26.460 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.460 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:26.462 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.462 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:26.463 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.463 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:26.464 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.465 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:26.466 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.466 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:26.467 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.467 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:26.468 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.469 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:26.470 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.470 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:26.470 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.470 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:26.472 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.472 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:26.474 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.474 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:26.475 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.475 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:26.476 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.476 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:26.477 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.477 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:26.478 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.478 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:26.481 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.481 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:26.482 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.482 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:26.483 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.483 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:26.485 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.485 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:26.486 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.486 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:26.487 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.488 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:26.489 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.489 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:26.490 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5504]
2025-09-13 15:16:26.490 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:26.521 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.525 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:26.526 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.528 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:26.530  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:26.530 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.532 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.533 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.537 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.540 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:26.542  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:26.542 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:26.544 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:26.545 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:26.547 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:26.548 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:28.509 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:28.510 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:28.510 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:28.510 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:28.510 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:28.513 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:28.513 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:28.514 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:28.515 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:28.534 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:28.535 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:28.538 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.538 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.538 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:28.539 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:28.538345900]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:28.538345900]
2025-09-13 15:16:28.543 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5505]
2025-09-13 15:16:28.546 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.546 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:28.547 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.548 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:28.549 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.549 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:28.551 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.551 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:28.552 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.552 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:28.554 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.554 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:28.555 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.555 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:28.556 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.556 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:28.558 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.558 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:28.559 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.559 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:28.561 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.561 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:28.562 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.562 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:28.564 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.564 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:28.565 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.565 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:28.567 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.567 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:28.568 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.568 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:28.569 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.569 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:28.571 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.571 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:28.572 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.572 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:28.574 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.574 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:28.575 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.575 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:28.577 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.577 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:28.578 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.578 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:28.579 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.579 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:28.580 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.580 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:28.582 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.582 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:28.584 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.584 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:28.585 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.586 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:28.587 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.587 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:28.588 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.588 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:28.590 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.590 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:28.591 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.591 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:28.592 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.593 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:28.594 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.594 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:28.596 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.596 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:28.597 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.597 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:28.598 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.598 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:28.599 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.600 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:28.600 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.600 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:28.602 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.602 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:28.604 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.604 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:28.605 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.605 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:28.607 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.607 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:28.608 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.608 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:28.609 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.609 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:28.611 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.611 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:28.612 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.612 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:28.614 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.614 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:28.615 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.615 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:28.617 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.617 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:28.619 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.619 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:28.620 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.620 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:28.621 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.621 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:28.622 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.622 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:28.624 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.624 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:28.625 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.625 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:28.627 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.627 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:28.628 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.628 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:28.629 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.629 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:28.630 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.630 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:28.632 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.632 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:28.633 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.633 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:28.634 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.634 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:28.636 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.636 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:28.638 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.638 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:28.640 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.640 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:28.641 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.641 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:28.643 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.643 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:28.644 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5505]
2025-09-13 15:16:28.644 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:28.656 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.657 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:28.659 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:28.662  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:28.663 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.664 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.666 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.670 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.674 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:28.675  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:28.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:28.677 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:28.678 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:28.680 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:28.681 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:29.348  INFO 6632 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://localhost/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-13 15:16:29.352  INFO 6632 --- [reactor-http-nio-17] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=RESPONSE, adjacentSystem=CCV, method='null', url='null', cachedPayload='null', requestQueryString='null', headers=[{Content-Type} = {text/html; charset=us-ascii}, {Server} = {Microsoft-HTTPAPI/2.0}, {Date} = {Sat, 13 Sep 2025 13:16:29 GMT}, {Connection} = {close}, {Content-Length} = {315}]}
2025-09-13 15:16:29.353 ERROR 6632 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:223)]
2025-09-13 15:16:29.353 ERROR 6632 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://localhost/api/app_settings failed with exception.
2025-09-13 15:16:30.660 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:30.660 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:30.660 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:30.660 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:30.660 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:30.661 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 15:16:30.667 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 15:16:30.668 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:30.671 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:30.670839600]
2025-09-13 15:16:30.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 15:16:30.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.675 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T15:16:30.670839600]
2025-09-13 15:16:30.676 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5506]
2025-09-13 15:16:30.678 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.678 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 15:16:30.680 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.680 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 15:16:30.681 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.681 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 15:16:30.682 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.682 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 15:16:30.684 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.684 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 15:16:30.686 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.686 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 15:16:30.687 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.687 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 15:16:30.688 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.688 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 15:16:30.690 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.690 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 15:16:30.691 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.691 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 15:16:30.693 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.693 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 15:16:30.694 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.694 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 15:16:30.695 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.695 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 15:16:30.696 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.696 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 15:16:30.697 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.697 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 15:16:30.698 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.698 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 15:16:30.700 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.700 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 15:16:30.701 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.701 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 15:16:30.703 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.703 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 15:16:30.704 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.704 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 15:16:30.705 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.705 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 15:16:30.707 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.707 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 15:16:30.708 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.708 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 15:16:30.709 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.709 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 15:16:30.710 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.710 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 15:16:30.712 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.712 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 15:16:30.713 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.713 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 15:16:30.714 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.715 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 15:16:30.716 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.716 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 15:16:30.717 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.717 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 15:16:30.718 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.718 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 15:16:30.720 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.720 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 15:16:30.721 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.722 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 15:16:30.723 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.723 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 15:16:30.725 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.725 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 15:16:30.726 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.726 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 15:16:30.728 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.728 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 15:16:30.729 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.729 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 15:16:30.730 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.730 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 15:16:30.731 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.731 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 15:16:30.733 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.733 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 15:16:30.735 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.735 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 15:16:30.736 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.736 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 15:16:30.737 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.738 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 15:16:30.739 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.739 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 15:16:30.741 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.741 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 15:16:30.742 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.742 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 15:16:30.743 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.743 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 15:16:30.744 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.744 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 15:16:30.746 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.746 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 15:16:30.747 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.747 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 15:16:30.748 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.749 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 15:16:30.750 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.750 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 15:16:30.752 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.752 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 15:16:30.753 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.753 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 15:16:30.754 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.754 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 15:16:30.755 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.756 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 15:16:30.757 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.757 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 15:16:30.758 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.758 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 15:16:30.760 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.760 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 15:16:30.761 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.761 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 15:16:30.762 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.763 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 15:16:30.763 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.763 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 15:16:30.765 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.765 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 15:16:30.767 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.767 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 15:16:30.768 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.768 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 15:16:30.769 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.770 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 15:16:30.772 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.772 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 15:16:30.773 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5506]
2025-09-13 15:16:30.773 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 15:16:30.797 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.800 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:30.806 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.812 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 15:16:30.813  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:30.813 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.815 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.816 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.818 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.819 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.821 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.822 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 15:16:30.823  WARN 6632 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 15:16:30.823 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:30.824 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:30.827 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 15:16:30.828 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 15:16:30.830 TRACE 6632 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
