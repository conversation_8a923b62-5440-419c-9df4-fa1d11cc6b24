2025-09-13 14:55:22.047  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 22452 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-13 14:55:22.055  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : No active profile set, falling back to 1 default profile: "default"
2025-09-13 14:55:22.167  INFO 22452 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-13 14:55:22.168  INFO 22452 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-13 14:55:23.945  INFO 22452 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-13 14:55:24.284  INFO 22452 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 323 ms. Found 15 JPA repository interfaces.
2025-09-13 14:55:25.450  INFO 22452 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-13 14:55:25.463  INFO 22452 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-13 14:55:25.463  INFO 22452 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-13 14:55:25.641  INFO 22452 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-13 14:55:25.641  INFO 22452 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3472 ms
2025-09-13 14:55:26.028  INFO 22452 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-13 14:55:26.240  INFO 22452 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-13 14:55:26.475  INFO 22452 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-13 14:55:26.649  INFO 22452 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-13 14:55:26.813  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-13 14:55:26.816  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-13 14:55:26.820  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-13 14:55:26.823  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-13 14:55:26.826  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-13 14:55:26.828  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-13 14:55:26.830  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-13 14:55:26.833  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-13 14:55:26.835  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-13 14:55:26.838  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-13 14:55:26.840  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-13 14:55:26.842  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-13 14:55:26.845  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-13 14:55:26.847  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-13 14:55:26.849  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-13 14:55:26.852  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-13 14:55:26.854  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-13 14:55:26.856  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-13 14:55:26.858  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-13 14:55:26.860  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-13 14:55:26.862  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-13 14:55:26.865  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-13 14:55:26.869  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-13 14:55:26.877  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-13 14:55:26.879  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-13 14:55:26.881  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-13 14:55:26.882  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-13 14:55:26.885  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-13 14:55:26.888  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-13 14:55:26.890  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-13 14:55:26.921  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-13 14:55:26.924  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-13 14:55:26.926  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-13 14:55:26.928  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-13 14:55:26.930  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-13 14:55:26.933  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-13 14:55:26.937  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-13 14:55:26.939  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-13 14:55:26.940  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-13 14:55:26.943  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-13 14:55:26.945  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-13 14:55:26.946  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-13 14:55:26.948  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-13 14:55:26.950  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-13 14:55:26.952  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-13 14:55:26.954  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-13 14:55:26.957  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-13 14:55:26.959  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-13 14:55:26.961  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-13 14:55:26.962  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-13 14:55:26.965  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-13 14:55:26.966  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-13 14:55:26.977  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-13 14:55:26.979  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-13 14:55:26.981  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-13 14:55:26.983  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-13 14:55:26.985  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-13 14:55:27.154  INFO 22452 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-13 14:55:27.322  INFO 22452 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-13 14:55:27.452  INFO 22452 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-13 14:55:27.516  INFO 22452 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-13 14:55:27.720  INFO 22452 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-13 14:55:27.876  INFO 22452 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-13 14:55:28.299  INFO 22452 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-13 14:55:28.305  INFO 22452 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-13 14:55:29.616  INFO 22452 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-13 14:55:29.628  INFO 22452 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-13 14:55:30.359  INFO 22452 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 120 ms
2025-09-13 14:55:31.450  INFO 22452 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 772 ms
2025-09-13 14:55:31.631  INFO 22452 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-13 14:55:31.710  INFO 22452 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 48 ms.
2025-09-13 14:55:31.969  INFO 22452 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-13 14:55:31.969  INFO 22452 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-13 14:55:32.049  INFO 22452 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 80 ms.
2025-09-13 14:55:32.973 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.005 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.010 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.015 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:33.061  WARN 22452 --- [restartedMain] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:33.065 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.070 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.076 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.105 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.113 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.118 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.125 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.129  WARN 22452 --- [restartedMain] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:33.130 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.134 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:33.139 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.143 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:33.147 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.151  WARN 22452 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ???????? ???? ??? ??????? ? AA 0000 AA ?? DP 0101 UA
2025-09-13 14:55:33.152  WARN 22452 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ?????? ????? ??? ??????? ? ??? ?? spl101
2025-09-13 14:55:33.237 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1001]
2025-09-13 14:55:33.243 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.248 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.252 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:33.257 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.260 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.290 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.297 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.301 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.304 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:33.307  WARN 22452 --- [restartedMain] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:33.307 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.310 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.314 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.319 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.322 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.325 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.329 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:33.332  WARN 22452 --- [restartedMain] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:33.333 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.336 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:33.342 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.345 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:33.349 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:33.415  INFO 22452 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=?? ? 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1?', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=?? ? 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='?? ??????, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@5f9d84e5}
2025-09-13 14:55:33.724 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5228]
2025-09-13 14:55:33.837 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [4]
2025-09-13 14:55:33.843 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3]
2025-09-13 14:55:33.846 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [2]
2025-09-13 14:55:33.851 TRACE 22452 --- [restartedMain] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:34.528  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2b7d7533, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3def5640, org.springframework.security.web.context.SecurityContextPersistenceFilter@393f845f, org.springframework.security.web.header.HeaderWriterFilter@d27d0b0e, org.springframework.security.web.authentication.logout.LogoutFilter@bf1be541, com.deb.spl.control.authorization.AuthenticationFilter@107dc797, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@586b2030, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@78ba4741, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@fa6b0ef9, org.springframework.security.web.session.SessionManagementFilter@272ee93c, org.springframework.security.web.access.ExceptionTranslationFilter@64d1feb7, org.springframework.security.web.access.intercept.AuthorizationFilter@df70b7ec]
2025-09-13 14:55:34.533  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.534  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-13 14:55:34.534  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.535  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-13 14:55:34.535  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.535  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-13 14:55:34.535  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.535  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-13 14:55:34.535  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.535  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-13 14:55:34.535  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.535  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-13 14:55:34.536  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.536  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-13 14:55:34.537  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.537  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-13 14:55:34.537  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.537  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-13 14:55:34.537  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.538  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-13 14:55:34.538  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.538  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-13 14:55:34.538  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.538  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-13 14:55:34.538  WARN 22452 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-13 14:55:34.538  INFO 22452 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-13 14:55:34.866  WARN 22452 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-13 14:55:35.624  INFO 22452 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-13 14:55:35.807  INFO 22452 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-13 14:55:35.889  INFO 22452 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-13 14:55:35.912  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 14.434 seconds (JVM running for 15.742)
2025-09-13 14:55:35.929  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -Dspring.application.admin.enabled=true -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9001 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=127.0.0.1 -cp D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar com.deb.spl.control.Application --spring.application.admin.enabled=true --spring.application.admin.jmx-name=org.springframework.boot:type=Admin,name=SpringApplication
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-13 14:55:35.930  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : EFC_10884_3789132940=1
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : EFC_10884_1262719628=1
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : EFC_10884_2283032206=1
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : EFC_10884_2775293581=1
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-13 14:55:35.932  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : EFC_10884_1592913036=1
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : IJ_RESTARTER_LOG=C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\log\restarter.log
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-13 14:55:35.933  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-13 14:55:35.935  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-13 14:55:35.936  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-13 14:55:35.936  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-13 14:55:35.936  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-13 14:55:35.937  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://localhost/api/asku/status/
2025-09-13 14:55:35.937  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-13 14:55:35.937  INFO 22452 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-13 14:55:35.957  INFO 22452 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-13 14:55:36.000  INFO 22452 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://localhost/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-13 14:55:36.000  INFO 22452 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-13 14:55:36.346  INFO 22452 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-13 14:55:36.346  INFO 22452 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-13 14:55:36.350  INFO 22452 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-09-13 14:55:37.455  WARN 22452 --- [http-nio-8079-exec-3] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [126] milliseconds.
2025-09-13 14:55:37.551  INFO 22452 --- [http-nio-8079-exec-3] c.vaadin.flow.spring.SpringInstantiator  : The number of beans implementing 'I18NProvider' is 0. Cannot use Spring beans for I18N, falling back to the default behavior
2025-09-13 14:55:37.705 ERROR 22452 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-13 14:55:37.714 ERROR 22452 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-13 14:55:37.737 ERROR 22452 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-13T14:55:37.736650600
2025-09-13 14:55:37.742  INFO 22452 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-13 14:55:37.758 ERROR 22452 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:37.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:37.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:37.865 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:37.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:37.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:37.868 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:37.868 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:37.868 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:37.869 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:37.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:37.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:37.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:37.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:37.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:37.872 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:37.873 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:37.873 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:37.874 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:37.875 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:37.875 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:37.876 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:38.060  INFO 22452 --- [reactor-http-nio-6] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=RESPONSE, adjacentSystem=CCV, method='null', url='null', cachedPayload='null', requestQueryString='null', headers=[{Content-Type} = {text/html; charset=us-ascii}, {Server} = {Microsoft-HTTPAPI/2.0}, {Date} = {Sat, 13 Sep 2025 12:55:37 GMT}, {Connection} = {close}, {Content-Length} = {315}]}
2025-09-13 14:55:38.191 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:38.191 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:38.192 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:38.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:38.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:38.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:38.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:38.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:38.194 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:38.195 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:38.197 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:38.197 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:38.197 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:38.197 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:38.197 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:38.197 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:38.198 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:38.198 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:38.198 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:38.198 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:38.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:38.249 ERROR 22452 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:223)]
2025-09-13 14:55:38.251 ERROR 22452 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://localhost/api/app_settings failed with exception.
2025-09-13 14:55:38.350 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.351 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.352 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:38.354 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.354 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.354 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:38.355 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:38.356 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:38.356 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:38.356 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:38.358 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:38.359 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:38.360 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:38.360 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:38.360 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:38.361 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:38.361 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:38.365 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:38.223957300]
2025-09-13 14:55:38.450 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.450 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.450 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:38.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:38.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:38.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:38.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:38.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:38.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:38.454 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:38.454 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:38.223957300]
2025-09-13 14:55:38.455 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5229]
2025-09-13 14:55:38.475 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.475 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:38.482 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.483 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:38.488 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.489 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:38.494 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.494 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:38.499 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.499 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:38.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:38.508 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.509 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:38.512 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.512 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:38.516 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.516 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:38.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:38.526 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.527 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:38.532 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.532 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:38.536 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.536 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:38.541 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.542 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:38.556 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.556 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:38.567 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.567 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:38.583 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.585 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:38.590 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.590 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:38.597 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.598 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:38.607 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.607 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:38.612 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.614 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:38.619 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.619 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:38.625 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.626 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:38.630 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.630 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:38.636 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.636 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:38.642 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.642 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:38.647 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.648 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:38.652 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.652 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:38.657 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.657 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:38.662 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.664 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:38.676 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.676 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:38.684 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.685 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:38.690 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.690 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:38.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:38.704 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.704 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:38.709 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:38.716 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.716 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:38.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.724 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:38.727 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.728 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:38.733 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.733 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:38.737 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.739 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:38.743 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.743 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:38.747 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.748 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:38.759 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.759 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:38.765 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:38.773 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.774 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:38.780 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.781 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:38.787 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.788 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:38.795 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.796 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:38.802 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.803 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:38.829 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.829 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:38.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:38.875 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:38.881 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.882 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:38.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:38.891 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.891 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:38.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:38.900 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.901 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:38.905 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.905 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:38.909 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.909 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:38.916 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.916 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:38.920 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.921 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:38.924 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.925 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:38.929 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.930 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:38.934 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.935 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:38.940 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.940 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:38.945 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:38.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:38.954 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5229]
2025-09-13 14:55:38.954 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:39.316 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BOOLEAN] - [false]
2025-09-13 14:55:39.316 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BOOLEAN] - [false]
2025-09-13 14:55:39.316 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BOOLEAN] - [false]
2025-09-13 14:55:39.325 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BOOLEAN] - [false]
2025-09-13 14:55:39.325 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BOOLEAN] - [false]
2025-09-13 14:55:39.326 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BOOLEAN] - [false]
2025-09-13 14:55:39.327 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BOOLEAN] - [false]
2025-09-13 14:55:39.333 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BOOLEAN] - [false]
2025-09-13 14:55:39.333 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BOOLEAN] - [false]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_SELECTED]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:39.334 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.338 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [TIMESTAMP] - [2025-09-13T14:55:39.309904400]
2025-09-13 14:55:39.500 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BOOLEAN] - [false]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BOOLEAN] - [false]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BOOLEAN] - [false]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [VARCHAR] - [NOT_SELECTED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BOOLEAN] - [false]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BOOLEAN] - [false]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [TIMESTAMP] - [null]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [VARCHAR] - [UNDEFINED]
2025-09-13 14:55:39.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [TIMESTAMP] - [2025-09-13T14:55:39.497620]
2025-09-13 14:55:39.564 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [TO]
2025-09-13 14:55:39.594 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [4]
2025-09-13 14:55:39.610 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [ASKU]
2025-09-13 14:55:39.616 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:39.627 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NPPA]
2025-09-13 14:55:39.633 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [2]
2025-09-13 14:55:39.643 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [DEA]
2025-09-13 14:55:39.648 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [3]
2025-09-13 14:55:39.665 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [{"id":1,"ppoBayType":"ASKU","firePresent":false,"unitsInPPoBay":{"BALLOON_ASKU_1":{"id":1,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_ASKU_1","bayType":"ASKU","unitStatus":"UNDEFINED"},"BALLOON_ASKU_2":{"id":2,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_ASKU_2","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_2":{"id":10,"unitType":"OPTICAL_SENSOR","unitName":"??2","unitAlias":"OPTICAL_SENSOR_ASKU_2","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_1":{"id":9,"unitType":"OPTICAL_SENSOR","unitName":"??1","unitAlias":"OPTICAL_SENSOR_ASKU_1","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_4":{"id":12,"unitType":"OPTICAL_SENSOR","unitName":"??4","unitAlias":"OPTICAL_SENSOR_ASKU_4","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_3":{"id":11,"unitType":"OPTICAL_SENSOR","unitName":"??3","unitAlias":"OPTICAL_SENSOR_ASKU_3","bayType":"ASKU","unitStatus":"UNDEFINED"}}};{"id":2,"ppoBayType":"NPPA","firePresent":false,"unitsInPPoBay":{"BALLOON_NPPA_2":{"id":4,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_NPPA_2","bayType":"NPPA","unitStatus":"UNDEFINED"},"BALLOON_NPPA_1":{"id":3,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_NPPA_1","bayType":"NPPA","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_NPPA_1":{"id":13,"unitType":"OPTICAL_SENSOR","unitName":"??1","unitAlias":"OPTICAL_SENSOR_NPPA_1","bayType":"NPPA","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_NPPA_2":{"id":14,"unitType":"OPTICAL_SENSOR","unitName":"??2","unitAlias":"OPTICAL_SENSOR_NPPA_2","bayType":"NPPA","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_NPPA_3":{"id":15,"unitType":"OPTICAL_SENSOR","unitName":"??3","unitAlias":"OPTICAL_SENSOR_NPPA_3","bayType":"NPPA","unitStatus":"UNDEFINED"}}};{"id":3,"ppoBayType":"DEA","firePresent":false,"unitsInPPoBay":{"BALLOON_DEA_2":{"id":6,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_DEA_2","bayType":"DEA","unitStatus":"UNDEFINED"},"BALLOON_DEA_1":{"id":5,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_DEA_1","bayType":"DEA","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_DEA_1":{"id":16,"unitType":"THERMAL_SENSOR","unitName":"??1","unitAlias":"THERMAL_SENSOR_DEA_1","bayType":"DEA","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_DEA_3":{"id":18,"unitType":"THERMAL_SENSOR","unitName":"??3","unitAlias":"THERMAL_SENSOR_DEA_3","bayType":"DEA","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_DEA_2":{"id":17,"unitType":"THERMAL_SENSOR","unitName":"??2","unitAlias":"THERMAL_SENSOR_DEA_2","bayType":"DEA","unitStatus":"UNDEFINED"}}};{"id":4,"ppoBayType":"TO","firePresent":false,"unitsInPPoBay":{"THERMAL_SENSOR_TO_2":{"id":20,"unitType":"THERMAL_SENSOR","unitName":"??2","unitAlias":"THERMAL_SENSOR_TO_2","bayType":"TO","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_TO_1":{"id":19,"unitType":"THERMAL_SENSOR","unitName":"??1","unitAlias":"THERMAL_SENSOR_TO_1","bayType":"TO","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_TO_3":{"id":21,"unitType":"THERMAL_SENSOR","unitName":"??3","unitAlias":"THERMAL_SENSOR_TO_3","bayType":"TO","unitStatus":"UNDEFINED"},"BALLOON_TO_1":{"id":7,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_TO_1","bayType":"TO","unitStatus":"UNDEFINED"},"BALLOON_TO_2":{"id":8,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_TO_2","bayType":"TO","unitStatus":"UNDEFINED"}}}]
2025-09-13 14:55:39.667 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:39.668 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [TIMESTAMP] - [2025-09-13T14:55:39.660122400]
2025-09-13 14:55:39.692 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [{"id":1,"ppoBayType":"ASKU","firePresent":false,"unitsInPPoBay":{"BALLOON_ASKU_1":{"id":1,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_ASKU_1","bayType":"ASKU","unitStatus":"UNDEFINED"},"BALLOON_ASKU_2":{"id":2,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_ASKU_2","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_2":{"id":10,"unitType":"OPTICAL_SENSOR","unitName":"??2","unitAlias":"OPTICAL_SENSOR_ASKU_2","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_1":{"id":9,"unitType":"OPTICAL_SENSOR","unitName":"??1","unitAlias":"OPTICAL_SENSOR_ASKU_1","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_4":{"id":12,"unitType":"OPTICAL_SENSOR","unitName":"??4","unitAlias":"OPTICAL_SENSOR_ASKU_4","bayType":"ASKU","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_ASKU_3":{"id":11,"unitType":"OPTICAL_SENSOR","unitName":"??3","unitAlias":"OPTICAL_SENSOR_ASKU_3","bayType":"ASKU","unitStatus":"UNDEFINED"}}};{"id":2,"ppoBayType":"NPPA","firePresent":false,"unitsInPPoBay":{"BALLOON_NPPA_2":{"id":4,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_NPPA_2","bayType":"NPPA","unitStatus":"UNDEFINED"},"BALLOON_NPPA_1":{"id":3,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_NPPA_1","bayType":"NPPA","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_NPPA_1":{"id":13,"unitType":"OPTICAL_SENSOR","unitName":"??1","unitAlias":"OPTICAL_SENSOR_NPPA_1","bayType":"NPPA","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_NPPA_2":{"id":14,"unitType":"OPTICAL_SENSOR","unitName":"??2","unitAlias":"OPTICAL_SENSOR_NPPA_2","bayType":"NPPA","unitStatus":"UNDEFINED"},"OPTICAL_SENSOR_NPPA_3":{"id":15,"unitType":"OPTICAL_SENSOR","unitName":"??3","unitAlias":"OPTICAL_SENSOR_NPPA_3","bayType":"NPPA","unitStatus":"UNDEFINED"}}};{"id":3,"ppoBayType":"DEA","firePresent":false,"unitsInPPoBay":{"BALLOON_DEA_2":{"id":6,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_DEA_2","bayType":"DEA","unitStatus":"UNDEFINED"},"BALLOON_DEA_1":{"id":5,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_DEA_1","bayType":"DEA","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_DEA_1":{"id":16,"unitType":"THERMAL_SENSOR","unitName":"??1","unitAlias":"THERMAL_SENSOR_DEA_1","bayType":"DEA","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_DEA_3":{"id":18,"unitType":"THERMAL_SENSOR","unitName":"??3","unitAlias":"THERMAL_SENSOR_DEA_3","bayType":"DEA","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_DEA_2":{"id":17,"unitType":"THERMAL_SENSOR","unitName":"??2","unitAlias":"THERMAL_SENSOR_DEA_2","bayType":"DEA","unitStatus":"UNDEFINED"}}};{"id":4,"ppoBayType":"TO","firePresent":false,"unitsInPPoBay":{"THERMAL_SENSOR_TO_2":{"id":20,"unitType":"THERMAL_SENSOR","unitName":"??2","unitAlias":"THERMAL_SENSOR_TO_2","bayType":"TO","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_TO_1":{"id":19,"unitType":"THERMAL_SENSOR","unitName":"??1","unitAlias":"THERMAL_SENSOR_TO_1","bayType":"TO","unitStatus":"UNDEFINED"},"THERMAL_SENSOR_TO_3":{"id":21,"unitType":"THERMAL_SENSOR","unitName":"??3","unitAlias":"THERMAL_SENSOR_TO_3","bayType":"TO","unitStatus":"UNDEFINED"},"BALLOON_TO_1":{"id":7,"unitType":"BALLOON","unitName":"????? 1","unitAlias":"BALLOON_TO_1","bayType":"TO","unitStatus":"UNDEFINED"},"BALLOON_TO_2":{"id":8,"unitType":"BALLOON","unitName":"????? 2","unitAlias":"BALLOON_TO_2","bayType":"TO","unitStatus":"UNDEFINED"}}}]
2025-09-13 14:55:39.692 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:39.692 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [TIMESTAMP] - [2025-09-13T14:55:39.660122400]
2025-09-13 14:55:39.693 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [1038]
2025-09-13 14:55:39.780 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:39.796 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:39.853 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:39.876 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:39.903  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:39.904 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:39.993 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:40.034 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:40.059 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:40.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:40.099 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:40.140 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:40.204  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:40.205 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:40.223 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:40.259 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:40.281 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:40.294 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:40.322  INFO 22452 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-13 14:55:40.338 ERROR 22452 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-13 14:55:41.741 ERROR 22452 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-13T14:55:41.741415300
2025-09-13 14:55:41.753 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:41.753 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:41.753 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:41.753 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:41.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:41.756 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:41.757 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:41.757 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:41.757 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:41.757 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:41.757 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:41.757 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:41.758 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:41.758 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:41.758 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:41.760 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:41.760 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:41.760 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:41.761 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:41.761 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:41.761 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:41.761 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:41.761 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:41.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:41.763 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:41.765 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:41.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:41.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:41.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:41.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:41.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:41.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:41.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:41.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:41.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:41.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:41.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:41.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:41.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:41.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:41.902 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:41.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:41.947 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:41.949 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:41.949 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:41.949 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:41.950 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:41.951 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:41.952 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:41.953 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:42.015 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.015 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.017 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:42.464 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:42.010418300]
2025-09-13 14:55:42.486 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.487 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.488 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:42.489 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.491 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:42.492 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:42.492 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:42.493 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:42.494 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:42.504 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:42.505 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:42.505 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:42.010418300]
2025-09-13 14:55:42.505 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5230]
2025-09-13 14:55:42.584 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.584 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:42.590 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.590 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:42.598  INFO 22452 --- [http-nio-8079-exec-1] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 6070 ms
2025-09-13 14:55:42.608 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.608 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:42.611 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.612 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:42.618 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.618 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:42.630 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.630 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:42.635 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.636 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:42.657 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.657 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:42.660 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.660 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:42.673 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.673 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:42.683 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.683 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:42.688 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.688 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:42.694 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.694 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:42.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:42.706 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.707 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:42.712 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.712 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:42.721 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.721 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:42.725 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.726 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:42.737 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.737 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:42.740 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.740 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:42.746 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.746 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:42.752 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.753 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:42.759 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.760 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:42.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.766 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:42.770 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.770 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:42.775 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.776 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:42.792 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.792 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:42.804 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.805 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:42.808 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.809 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:42.813 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.813 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:42.817 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.818 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:42.820 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.822 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:42.827 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.829 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:42.838 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.839 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:42.847 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:42.852 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.853 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:42.856 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.856 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:42.859 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.860 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:42.863 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.863 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:42.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.867 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:42.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.870 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:42.873 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.873 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:42.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:42.880 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.880 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:42.883 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.884 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:42.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:42.890 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.890 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:42.893 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.893 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:42.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:42.899 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.899 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:42.902 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.902 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:42.905 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.905 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:42.908 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.908 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:42.912 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.912 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:42.917 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.917 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:42.925 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.925 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:42.929 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.929 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:42.934 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.935 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:42.938 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.939 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:42.944 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.944 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:42.948 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.948 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:42.952 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.953 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:42.957 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.957 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:42.961 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.962 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:42.968 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.968 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:42.973 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.973 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:42.976 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.977 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:42.980 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.980 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:42.985 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5230]
2025-09-13 14:55:42.986 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:43.076 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.088 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:43.095 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.102 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:43.109  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:43.111 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.119 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.134 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.139 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.145 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.150 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:43.155  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:43.156 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:43.160 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:43.168 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:43.173 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:43.178 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:43.186  INFO 22452 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-13 14:55:43.194 ERROR 22452 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-13 14:55:43.274  INFO 22452 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-13 14:55:45.062 ERROR 22452 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-13T14:55:45.062058500
2025-09-13 14:55:45.067 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:45.068 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:45.069 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:45.070 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:45.071 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:45.103 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:45.103 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:45.103 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:45.104 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:45.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:45.113 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.113 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.113 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:45.114 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:45.111086500]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:45.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:45.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:45.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:45.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:45.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:45.111086500]
2025-09-13 14:55:45.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5231]
2025-09-13 14:55:45.130 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.131 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:45.134 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.134 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:45.137 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.137 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:45.140 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.140 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:45.143 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.143 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:45.146 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.147 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:45.150 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.150 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:45.153 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.153 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:45.156 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.157 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:45.160 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.160 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:45.163 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.164 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:45.167 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.167 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:45.170 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.170 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:45.173 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.174 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:45.176 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.177 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:45.179 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.179 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:45.182 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.183 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:45.185 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.185 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:45.188 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.188 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:45.190 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.190 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:45.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.193 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:45.196 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.196 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:45.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.199 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:45.202 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.203 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:45.205 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.205 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:45.207 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.207 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:45.209 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.210 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:45.212 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.213 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:45.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:45.218 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.218 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:45.220 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.220 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:45.223 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.223 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:45.225 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.225 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:45.227 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.227 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:45.230 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.230 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:45.233 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.233 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:45.235 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.236 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:45.237 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.238 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:45.240 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.240 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:45.242 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.242 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:45.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:45.247 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.247 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:45.250 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.250 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:45.252 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.252 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:45.255 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.255 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:45.257 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.258 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:45.260 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.260 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:45.316 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.316 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:45.321 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.321 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:45.327 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.327 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:45.338 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.338 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:45.352 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.353 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:45.357 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.357 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:45.360 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.360 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:45.363 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.363 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:45.366 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.367 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:45.370 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.370 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:45.372 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.372 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:45.375 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.375 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:45.378 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.378 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:45.381 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.382 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:45.391 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.391 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:45.395 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.395 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:45.399 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.399 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:45.403 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.403 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:45.405 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.405 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:45.407 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.407 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:45.410 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.410 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:45.412 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5231]
2025-09-13 14:55:45.413 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:45.453 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.459 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:45.462 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.466 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:45.470  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:45.470 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.474 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.477 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.483 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.486 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.489 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.493 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:45.496  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:45.497 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:45.500 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:45.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:45.506 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:45.510 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:47.461 ERROR 22452 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-13T14:55:47.461557500
2025-09-13 14:55:47.467 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:47.468 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:47.469 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:47.501 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:47.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:47.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:47.509 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.510 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:47.511 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:47.508193600]
2025-09-13 14:55:47.519 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.519 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:47.520 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:47.521 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:47.521 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:47.508193600]
2025-09-13 14:55:47.521 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5232]
2025-09-13 14:55:47.526 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.526 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:47.529 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.529 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:47.535 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.535 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:47.538 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.538 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:47.542 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.542 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:47.545 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.545 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:47.549 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.549 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:47.556 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.557 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:47.586 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.586 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:47.590 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.590 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:47.593 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.594 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:47.596 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.597 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:47.604 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.604 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:47.607 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.607 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:47.609 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.610 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:47.614 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.614 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:47.618 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.618 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:47.621 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.622 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:47.624 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.624 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:47.627 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.627 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:47.628 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.629 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:47.631 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.631 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:47.633 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.633 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:47.635 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.635 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:47.636 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.637 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:47.639 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.639 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:47.640 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.641 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:47.643 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.643 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:47.645 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.645 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:47.647 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.647 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:47.649 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.649 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:47.651 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.651 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:47.653 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.653 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:47.655 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.655 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:47.657 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.657 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:47.659 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.659 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:47.661 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.661 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:47.663 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.663 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:47.665 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.666 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:47.668 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.668 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:47.670 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.670 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:47.673 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.673 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:47.675 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.675 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:47.678 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.678 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:47.681 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.681 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:47.684 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.684 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:47.687 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.687 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:47.690 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.690 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:47.692 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.692 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:47.695 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.695 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:47.698 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.698 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:47.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:47.704 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.704 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:47.707 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.707 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:47.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:47.712 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.713 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:47.715 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.716 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:47.717 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.718 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:47.726 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.726 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:47.730 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.730 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:47.733 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.733 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:47.736 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.736 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:47.738 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.739 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:47.741 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.741 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:47.744 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.744 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:47.746 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.746 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:47.749 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.749 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:47.751 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.751 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:47.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5232]
2025-09-13 14:55:47.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:47.845 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:47.855 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.858 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:47.862  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:47.862 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.866 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.868 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.873 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.881 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.885 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:47.889  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:47.890 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:47.892 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:47.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:47.900 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:47.903 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:48.186  INFO 22452 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-13 14:55:48.190 ERROR 22452 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-13 14:55:49.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:49.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:49.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:49.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:49.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:49.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:49.850 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:49.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:49.877 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:49.878 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:49.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:49.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:49.888 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:49.885391500]
2025-09-13 14:55:49.894 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:49.895 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:49.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:49.885391500]
2025-09-13 14:55:49.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5233]
2025-09-13 14:55:49.904 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.904 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:49.907 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.907 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:49.911 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.911 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:49.914 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.914 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:49.916 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.916 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:49.919 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.919 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:49.923 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.923 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:49.927 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.927 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:49.930 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.930 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:49.933 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.933 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:49.936 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.936 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:49.938 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.938 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:49.941 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.941 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:49.943 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.944 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:49.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.946 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:49.949 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.949 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:49.952 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.952 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:49.955 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.955 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:49.957 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.958 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:49.960 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.960 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:49.962 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.962 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:49.964 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.965 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:49.967 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.968 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:49.970 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.970 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:49.973 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.973 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:49.975 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.975 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:49.978 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.978 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:49.980 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.980 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:49.982 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.982 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:49.986 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.986 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:49.989 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.989 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:49.992 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.992 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:49.995 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.995 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:49.998 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:49.998 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:50.001 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.001 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:50.004 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.005 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:50.007 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.007 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:50.011 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.011 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:50.014 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.014 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:50.017 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.018 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:50.021 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.021 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:50.024 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.024 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:50.027 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.027 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:50.031 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.031 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:50.035 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.035 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:50.038 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.038 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:50.042 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.042 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:50.046 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.046 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:50.049 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.050 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:50.057 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.057 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:50.060 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.061 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:50.075 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.075 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:50.082 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.082 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:50.085 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.085 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:50.088 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.088 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:50.091 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.091 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:50.094 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.094 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:50.096 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.097 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:50.099 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.099 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:50.102 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.102 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:50.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.105 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:50.107 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.107 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:50.110 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.110 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:50.113 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.113 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:50.119 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.119 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:50.124 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.125 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:50.127 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.127 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:50.132 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.132 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:50.137 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5233]
2025-09-13 14:55:50.137 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:50.208 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.225 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:50.235 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.240 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:50.246  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:50.247 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.254 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.259 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.262 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.265 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.269 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:50.272  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:50.272 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:50.276 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:50.280 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:50.284 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:50.287 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:50.379  INFO 22452 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-13 14:55:50.380  INFO 22452 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 6990 ms
2025-09-13 14:55:50.381  INFO 22452 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 14423ms
2025-09-13 14:55:50.381  INFO 22452 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-13 14:55:50.382  INFO 22452 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ?  Local:   http://127.0.0.1:2825/VAADIN/
2025-09-13 14:55:52.115  INFO 22452 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-13 14:55:52.115  INFO 22452 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-13 14:55:52.214 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:52.215 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:52.216 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:52.243 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:52.244 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:52.250 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:52.251 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:52.252 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:52.252 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:52.252 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:52.248971100]
2025-09-13 14:55:52.262 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.262 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.263 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:52.263 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.263 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:52.248971100]
2025-09-13 14:55:52.264 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5234]
2025-09-13 14:55:52.270 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.270 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:52.280 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.280 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:52.308 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.308 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:52.317 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.318 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:52.322 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.322 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:52.327 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.328 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:52.333 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.333 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:52.337 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.337 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:52.340 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.340 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:52.343 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.343 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:52.352 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.353 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:52.366 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.366 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:52.374 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.374 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:52.376 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.377 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:52.379 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.379 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:52.381 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.381 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:52.383 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.383 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:52.386 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.386 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:52.387 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.387 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:52.391 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.391 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:52.394 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.394 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:52.398 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.399 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:52.402 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.402 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:52.406 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.406 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:52.409 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.409 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:52.412 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.413 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:52.416 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.416 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:52.418 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.418 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:52.421 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.421 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:52.424 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.424 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:52.426 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.427 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:52.429 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.430 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:52.432 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.433 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:52.435 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.435 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:52.438 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.438 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:52.441 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.441 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:52.443 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.443 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:52.446 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.446 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:52.450 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.450 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:52.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.452 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:52.455 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.456 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:52.459 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.459 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:52.470 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.472 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:52.476 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.476 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:52.478 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.479 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:52.482 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.482 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:52.485 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.485 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:52.487 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.488 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:52.492 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.493 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:52.497 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.497 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:52.500 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.500 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:52.502 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.503 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:52.506 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.507 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:52.509 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.510 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:52.513 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.513 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:52.515 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.516 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:52.518 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.518 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:52.521 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.521 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:52.524 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.524 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:52.526 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.526 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:52.529 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.529 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:52.531 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.531 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:52.534 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.534 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:52.536 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.536 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:52.540 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.540 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:52.542 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.542 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:52.545 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.545 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:52.547 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.547 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:52.550 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5234]
2025-09-13 14:55:52.550 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:52.677 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.696 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:52.703 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.708 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:52.715  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:52.715 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.720 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.724 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.732 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.738 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.746 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.754 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:52.762  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:52.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:52.775 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:52.791 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:52.801 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:52.807 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:53.189  INFO 22452 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-13 14:55:53.195 ERROR 22452 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:54.671 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:54.672 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [31]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [BIGINT] - [27]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [BIGINT] - [62]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [BIGINT] - [8]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [BIGINT] - [68]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [BIGINT] - [11]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [BIGINT] - [49]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [BIGINT] - [53]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [BIGINT] - [66]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [BIGINT] - [19]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [BIGINT] - [32]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [BIGINT] - [39]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [BIGINT] - [51]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [BIGINT] - [15]
2025-09-13 14:55:54.700 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [BIGINT] - [28]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [BIGINT] - [50]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [BIGINT] - [38]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [25]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [20] as [BIGINT] - [34]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [21] as [BIGINT] - [47]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [22] as [BIGINT] - [52]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [23] as [BIGINT] - [23]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [24] as [BIGINT] - [48]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [25] as [BIGINT] - [21]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [26] as [BIGINT] - [1]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [27] as [BIGINT] - [69]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [28] as [BIGINT] - [18]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [29] as [BIGINT] - [43]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [30] as [BIGINT] - [57]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [31] as [BIGINT] - [60]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [32] as [BIGINT] - [29]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [33] as [BIGINT] - [5]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [34] as [BIGINT] - [36]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [35] as [BIGINT] - [9]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [36] as [BIGINT] - [37]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [37] as [BIGINT] - [7]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [38] as [BIGINT] - [12]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [39] as [BIGINT] - [41]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [40] as [BIGINT] - [13]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [41] as [BIGINT] - [54]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [42] as [BIGINT] - [67]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [43] as [BIGINT] - [42]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [44] as [BIGINT] - [45]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [45] as [BIGINT] - [40]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [46] as [BIGINT] - [14]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [47] as [BIGINT] - [44]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [48] as [BIGINT] - [55]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [49] as [BIGINT] - [61]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [50] as [BIGINT] - [58]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [51] as [BIGINT] - [63]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [52] as [BIGINT] - [20]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [53] as [BIGINT] - [17]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [54] as [BIGINT] - [56]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [55] as [BIGINT] - [65]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [56] as [BIGINT] - [64]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [57] as [BIGINT] - [3]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [58] as [BIGINT] - [59]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [59] as [BIGINT] - [33]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [60] as [BIGINT] - [16]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [61] as [BIGINT] - [4]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [62] as [BIGINT] - [30]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [63] as [BIGINT] - [6]
2025-09-13 14:55:54.701 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [64] as [BIGINT] - [46]
2025-09-13 14:55:54.702 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [65] as [BIGINT] - [24]
2025-09-13 14:55:54.702 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [66] as [BIGINT] - [22]
2025-09-13 14:55:54.702 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [67] as [BIGINT] - [35]
2025-09-13 14:55:54.702 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [68] as [BIGINT] - [10]
2025-09-13 14:55:54.702 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [69] as [BIGINT] - [26]
2025-09-13 14:55:54.709 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:54.710 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:54.711 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:54.711 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:54.707449900]
2025-09-13 14:55:54.721 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [3] as [VARCHAR] - [{1,??????????? ?????????????? ????,chassisHorizontalAlignment,OFF};{2,???? ? ??????????????? ?????????,isChassisHorizontal,OFF};{3,?????????????? ?????????,isAlignmentImpossible,OFF};{4,??????????? ??????????? ?????????? ? ??,isOutriggersMovingToPP,OFF};{5,????????? ? ????????? ????????? (??),isOutriggersInMobileState,OFF};{6,???????? ???????? ??????????? ?? ??????????????,isEmergencyHangingAndAlignment,OFF};{7,??????? (???????) ????????? ????????? ????? ????????? ?????,leftFrontOutriggerInitialPosition,OFF};{8,??????? (???????) ????????? ????????? ?????? ????????? ?????,rightFrontOutriggerInitialPosition,OFF};{9,??????? (???????) ????????? ????????? ?????? ??????? ?????,rightRearOutriggerInitialPosition,OFF};{10,??????? (???????) ????????? ????????? ????? ??????? ?????,leftRearOutriggerInitialPosition,OFF};{11,?????? ????????????,armUnlocked,OFF};{12,??????????? ?????? ??????,armRaising,OFF};{13,?????? ???????,armRaised,OFF};{14,????? ???????????? ?????????????,leftGasSpringUnlocked,OFF};{15,?????? ???????????? ?????????????,rightGasSpringUnlocked,OFF};{16,????????? ?????? ?????????????,isLoweringLeftGasSpring,OFF};{17,????????? ??????? ?????????????,isLoweringRightGasSpring,OFF};{18,???????? ???????? (?????? ?? ??????????????),emergencySituationForArmAndGasSprings,OFF};{19,????? ???????????? ????????,leftGasSpringLowered,OFF};{20,?????? ???????????? ????????,rightGasSpringLowered,OFF};{21,??????????? ?????? ?????? ?????????????,isRaisingLeftGasSpring,OFF};{22,??????????? ?????? ??????? ?????????????,isRaisingRightGasSpring,OFF};{23,????? ???????????? ? ????????? ????????? (??),leftGasSpringInMobileState,OFF};{24,?????? ???????????? ? ????????? ????????? (??),rightGasSpringInMobileState,OFF};{25,????? ???????????? ????????????,leftGasSpringLocked,OFF};{26,?????? ???????????? ????????????,rightGasSpringLocked,OFF};{27,????????? ??????,isLoweringArm,OFF};{28,?????? ? ????????? ????????? (??),armInMobileState,OFF};{29,?????? ???????????,boomLocked,OFF};{30,?????? ? ????????? ????????? (??),boomInEndPosition,OFF};{31,???????????? ?? ??????,boomEPMalfunction,OFF};{32,???????????? ?? ?????????? ??????,boomLockingEPMalfunction,OFF};{33,???????????? ?? ?????????? ?????? ?????????????,leftGasSpringLockingEPMalfunction,OFF};{34,???????????? ?? ?????????? ??????? ?????????????,rightGasSpringLockingEPMalfunction,OFF};{35,???????????? ?? ?????? ?????????????,malfunctionLeftGasReflectorEP,OFF};{36,???????????? ?? ??????? ?????????????,malfunctionRightGasReflectorEP,OFF};{37,???????????? ???? ? ?? ?????? (??????? ?????????),armEmptyStrokeMaxPressure,OFF};{38,???????????? ???? ? ?? ?????? (???????? ?????????),armPistonStrokeMaxPressure,OFF};{39,?????? ??????? ???,SPLEngineStarting,OFF};{40,?????? ??? ????????,SPLEngineStarted,OFF};{41,??????????? ????????? ?????? ?? (?1),mainPumpConnectionToGS_N1,OFF};{42,???????? ????? ??????????? ?? ?? (?1),mainPumpConnectedToGS_N1,OFF};{43,????????????? ????????????? ?????????,unloadingElectromagnetEnabled,OFF};{44,??????????? ??????? ??????? ???,SPLEnginestopping,OFF};{45,???????? ????????,emergencySituation,OFF};{46,?????? ??????? ??????????,engineStartImpossible,OFF};{47,???????????? ??????????? ??????? ?????????,engineStartImpossible2,OFF};{48,??????? ??????? ?????????,engineStopImpossible,OFF};{49,??????,fire,OFF};{50,??????????? ?????? ???-1,pollutedFilterDZF1,OFF};{51,??????????? ?????? ???-2,pollutedFilterDZF2,OFF};{52,???? ? ?????????? ?????????? ?? ? ?????,airPressureNotNormal,OFF};{53,???????? ????? ????? ?????? ??? ????????,leftFrontLockLeftTPKClosed,OFF};{54,???????? ????? ????? ?????? ??? ?????????,leftFrontLockLeftTPKOpened,OFF};{55,???????? ?????? ????? ?????? ??? ????????,rightFrontLockLeftTPKClosed,OFF};{56,???????? ?????? ????? ?????? ??? ?????????,rightFrontLockLeftTPKOpened,OFF};{57,?????? ?????? ????? ?????? ??? ????????,rightRearLockLeftTPKClosed,OFF};{58,?????? ?????? ????? ?????? ??? ?????????,rightRearLockLeftTPKOpened,OFF};{59,?????? ????? ????? ?????? ??? ????????,leftRearLockLeftTPKClosed,OFF};{60,?????? ????? ????? ?????? ??? ?????????,leftRearLockLeftTPKOpened,OFF};{61,???????? ????? ????? ??????? ??? ????????,leftFrontLockRightTPKClosed,OFF};{62,???????? ????? ????? ??????? ??? ?????????,leftFrontLockRightTPKOpened,OFF};{63,???????? ?????? ????? ??????? ??? ????????,rightFrontLockRightTPKClosed,OFF};{64,???????? ?????? ????? ??????? ??? ?????????,rightFrontLockRightTPKOpened,OFF};{65,?????? ?????? ????? ??????? ??? ????????,rightRearLockRightTPKClosed,OFF};{66,?????? ?????? ????? ??????? ??? ?????????,rightRearLockRightTPKOpened,OFF};{67,?????? ????? ????? ??????? ??? ????????,leftRearLockRightTPKClosed,OFF};{68,?????? ????? ????? ??????? ??? ?????????,leftRearLockRightTPKOpened,OFF};{69,???? ??? ????,SUTOStop,OFF}]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [4] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [5] as [VARCHAR] - [NONE]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [6] as [INTEGER] - [0]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [7] as [INTEGER] - [0]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [8] as [INTEGER] - [0]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [9] as [INTEGER] - [0]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [10] as [INTEGER] - [0]
2025-09-13 14:55:54.722 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [11] as [DOUBLE] - [0.0]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [12] as [DOUBLE] - [0.0]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [13] as [DOUBLE] - [0.0]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [14] as [INTEGER] - [0]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [15] as [INTEGER] - [0]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [16] as [VARCHAR] - [NOT_CONNECTED]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [17] as [VARCHAR] - [UNKNOWN]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [18] as [TIMESTAMP] - [2025-09-13T14:55:54.707449900]
2025-09-13 14:55:54.723 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [19] as [BIGINT] - [5235]
2025-09-13 14:55:54.731 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.732 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [31]
2025-09-13 14:55:54.735 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.735 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [2]
2025-09-13 14:55:54.738 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.738 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [27]
2025-09-13 14:55:54.741 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.742 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [62]
2025-09-13 14:55:54.745 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.745 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [8]
2025-09-13 14:55:54.748 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.748 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [68]
2025-09-13 14:55:54.752 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.752 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [11]
2025-09-13 14:55:54.755 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.755 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [49]
2025-09-13 14:55:54.758 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.758 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [53]
2025-09-13 14:55:54.761 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.762 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [66]
2025-09-13 14:55:54.764 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.765 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [19]
2025-09-13 14:55:54.768 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.768 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [32]
2025-09-13 14:55:54.770 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.770 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [39]
2025-09-13 14:55:54.774 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.774 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [51]
2025-09-13 14:55:54.777 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.777 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [15]
2025-09-13 14:55:54.780 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.780 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [28]
2025-09-13 14:55:54.784 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.785 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [50]
2025-09-13 14:55:54.788 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.788 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [38]
2025-09-13 14:55:54.790 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.791 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [25]
2025-09-13 14:55:54.793 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.794 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [34]
2025-09-13 14:55:54.796 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.796 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [47]
2025-09-13 14:55:54.799 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.800 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [52]
2025-09-13 14:55:54.802 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.803 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [23]
2025-09-13 14:55:54.806 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.806 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [48]
2025-09-13 14:55:54.808 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.809 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [21]
2025-09-13 14:55:54.811 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.812 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [1]
2025-09-13 14:55:54.815 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.815 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [69]
2025-09-13 14:55:54.817 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.817 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [18]
2025-09-13 14:55:54.820 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.820 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [43]
2025-09-13 14:55:54.822 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.823 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [57]
2025-09-13 14:55:54.826 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.827 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [60]
2025-09-13 14:55:54.830 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.830 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [29]
2025-09-13 14:55:54.834 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.834 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [5]
2025-09-13 14:55:54.836 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.838 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [36]
2025-09-13 14:55:54.840 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.840 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [9]
2025-09-13 14:55:54.843 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.843 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [37]
2025-09-13 14:55:54.845 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.846 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [7]
2025-09-13 14:55:54.848 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.849 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [12]
2025-09-13 14:55:54.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.851 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [41]
2025-09-13 14:55:54.854 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.854 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [13]
2025-09-13 14:55:54.856 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.856 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [54]
2025-09-13 14:55:54.859 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.859 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [67]
2025-09-13 14:55:54.861 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.861 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [42]
2025-09-13 14:55:54.863 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.864 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [45]
2025-09-13 14:55:54.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.879 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [40]
2025-09-13 14:55:54.882 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.882 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [14]
2025-09-13 14:55:54.885 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.885 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [44]
2025-09-13 14:55:54.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.887 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [55]
2025-09-13 14:55:54.890 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.890 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [61]
2025-09-13 14:55:54.893 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.893 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [58]
2025-09-13 14:55:54.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.896 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [63]
2025-09-13 14:55:54.899 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.899 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [20]
2025-09-13 14:55:54.901 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.901 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [17]
2025-09-13 14:55:54.903 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.903 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [56]
2025-09-13 14:55:54.906 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.906 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [65]
2025-09-13 14:55:54.909 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.909 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [64]
2025-09-13 14:55:54.911 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.911 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [3]
2025-09-13 14:55:54.913 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.914 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [59]
2025-09-13 14:55:54.916 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.916 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [33]
2025-09-13 14:55:54.918 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.918 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [16]
2025-09-13 14:55:54.920 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.921 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [4]
2025-09-13 14:55:54.923 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.923 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [30]
2025-09-13 14:55:54.924 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.925 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [6]
2025-09-13 14:55:54.927 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.927 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [46]
2025-09-13 14:55:54.929 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.929 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [24]
2025-09-13 14:55:54.931 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.931 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [22]
2025-09-13 14:55:54.933 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.933 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [35]
2025-09-13 14:55:54.936 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.936 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [10]
2025-09-13 14:55:54.938 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [5235]
2025-09-13 14:55:54.938 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [2] as [BIGINT] - [26]
2025-09-13 14:55:54.985 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:54.992 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:54.998 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.002 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [255]
2025-09-13 14:55:55.009  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:55.011 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.015 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.019 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.025 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.029 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.034 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.036 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [1]
2025-09-13 14:55:55.039  WARN 22452 --- [scheduling-1] o.h.h.internal.ast.QueryTranslatorImpl   : HHH000104: firstResult/maxResults specified with collection fetch; applying in memory!
2025-09-13 14:55:55.040 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:55.044 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:55.049 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
2025-09-13 14:55:55.053 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [235]
2025-09-13 14:55:55.057 TRACE 22452 --- [scheduling-1] o.h.type.descriptor.sql.BasicBinder      : binding parameter [1] as [BIGINT] - [204]
