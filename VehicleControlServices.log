2025-09-04 14:37:01.646  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 25232 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 14:37:01.655  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 14:37:01.786  INFO 25232 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 14:37:01.787  INFO 25232 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 14:37:13.490  INFO 25232 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 14:37:13.875  INFO 25232 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 367 ms. Found 15 JPA repository interfaces.
2025-09-04 14:37:15.115  INFO 25232 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 14:37:15.301  INFO 25232 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 13512 ms
2025-09-04 14:37:15.785  INFO 25232 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 14:37:16.107  INFO 25232 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 14:37:18.012  INFO 25232 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 14:37:18.148  INFO 25232 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 14:37:18.475  INFO 25232 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 14:37:18.695  INFO 25232 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 14:37:19.157  INFO 25232 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:37:19.167  INFO 25232 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:37:21.296  INFO 25232 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 14:37:21.309  INFO 25232 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:37:22.193  INFO 25232 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 109 ms
2025-09-04 14:37:23.277  INFO 25232 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 818 ms
2025-09-04 14:37:23.478  INFO 25232 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 14:37:23.580  INFO 25232 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 66 ms.
2025-09-04 14:37:23.884  INFO 25232 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 14:37:23.885  INFO 25232 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 14:37:24.924  INFO 25232 --- [ForkJoinPool.commonPool-worker-2] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 1039 ms.
2025-09-04 14:37:25.095  WARN 25232 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 14:37:25.096  WARN 25232 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 14:37:25.314  INFO 25232 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@5985554}
2025-09-04 14:37:26.527  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@c67c838e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@39b01156, org.springframework.security.web.context.SecurityContextPersistenceFilter@3dda0c7b, org.springframework.security.web.header.HeaderWriterFilter@c6949361, org.springframework.security.web.authentication.logout.LogoutFilter@6728dd7a, com.deb.spl.control.authorization.AuthenticationFilter@8857964b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@264b021e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@47dbc77a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4d47747e, org.springframework.security.web.session.SessionManagementFilter@c054441b, org.springframework.security.web.access.ExceptionTranslationFilter@ab8e3d71, org.springframework.security.web.access.intercept.AuthorizationFilter@80686d16]
2025-09-04 14:37:26.533  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.534  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-04 14:37:26.536  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.536  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-04 14:37:26.536  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.537  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-04 14:37:26.537  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.537  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-04 14:37:26.537  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.537  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-04 14:37:26.538  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.538  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-04 14:37:26.538  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.538  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-04 14:37:26.538  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.538  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-04 14:37:26.538  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.538  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-04 14:37:26.538  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.538  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-04 14:37:26.539  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.539  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-04 14:37:26.539  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.539  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-04 14:37:26.539  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.539  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-04 14:37:26.539  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.539  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-04 14:37:26.539  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.539  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-04 14:37:26.539  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.539  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-04 14:37:26.541  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.541  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-04 14:37:26.541  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.541  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-04 14:37:26.541  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.542  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-04 14:37:26.542  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.542  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-04 14:37:26.542  WARN 25232 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:37:26.542  INFO 25232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-04 14:37:27.009  WARN 25232 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-04 14:37:27.978  INFO 25232 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-04 14:37:28.163 ERROR 25232 --- [restartedMain] org.atmosphere.cpr.AtmosphereFramework   : Failed to initialize Atmosphere Framework

java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.initAtmosphereForVaadinServlet(JSR356WebsocketInitializer.java:186) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.init(JSR356WebsocketInitializer.java:151) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.VaadinWebsocketEndpointExporter.registerEndpoints(VaadinWebsocketEndpointExporter.java:51) ~[vaadin-spring-23.3.3.jar:na]
	at org.springframework.web.socket.server.standard.ServerEndpointExporter.afterSingletonsInstantiated(ServerEndpointExporter.java:112) ~[spring-websocket-5.3.25.jar:5.3.25]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974) ~[spring-beans-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:41) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]

2025-09-04 14:37:28.165  WARN 25232 --- [restartedMain] c.v.f.s.c.JSR356WebsocketInitializer     : Failed to initialize Atmosphere for springServlet

java.lang.RuntimeException: Atmosphere init failed
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:253) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.initAtmosphereForVaadinServlet(JSR356WebsocketInitializer.java:186) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.init(JSR356WebsocketInitializer.java:151) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.VaadinWebsocketEndpointExporter.registerEndpoints(VaadinWebsocketEndpointExporter.java:51) ~[vaadin-spring-23.3.3.jar:na]
	at org.springframework.web.socket.server.standard.ServerEndpointExporter.afterSingletonsInstantiated(ServerEndpointExporter.java:112) ~[spring-websocket-5.3.25.jar:5.3.25]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974) ~[spring-beans-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:41) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]
Caused by: javax.servlet.ServletException: java.lang.IllegalStateException: Shutdown in progress
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:946) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	... 19 common frames omitted
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	... 21 common frames omitted

2025-09-04 14:37:28.248  INFO 25232 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-04 14:37:28.326 ERROR 25232 --- [restartedMain] org.atmosphere.cpr.AtmosphereFramework   : Failed to initialize Atmosphere Framework

java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.3.jar:23.3.3]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8.jar:2.7.8]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:522) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:512) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:239) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:41) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]

2025-09-04 14:37:28.329  WARN 25232 --- [restartedMain] c.v.flow.server.VaadinServletService     : Error initializing Atmosphere. Push will not work.

com.vaadin.flow.server.ServiceException: Failed to initialize Atmosphere for springServlet. Push will not work.
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:100) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.3.jar:23.3.3]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8.jar:2.7.8]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:522) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:512) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:239) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:41) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]
Caused by: java.lang.RuntimeException: Atmosphere init failed
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:253) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.3.jar:23.3.3]
	... 47 common frames omitted
Caused by: javax.servlet.ServletException: java.lang.IllegalStateException: Shutdown in progress
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:946) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	... 48 common frames omitted
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	... 50 common frames omitted

2025-09-04 14:37:28.357  INFO 25232 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-04 14:37:28.394  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 27.587 seconds (JVM running for 29.622)
2025-09-04 14:37:28.408  INFO 25232 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-04 14:37:28.417  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-04 14:37:28.418  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-04 14:37:28.419  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-04 14:37:28.419  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" "-javaagent:D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar=14190" -Dfile.encoding=UTF-8 -classpath D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar com.deb.spl.control.Application
2025-09-04 14:37:28.419  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-04 14:37:28.420  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-04 14:37:28.420  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-04 14:37:28.420  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-04 14:37:28.420  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-04 14:37:28.420  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-04 14:37:28.420  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_3789132940=1
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1262719628=1
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-04 14:37:28.421  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-04 14:37:28.422  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-04 14:37:28.422  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-04 14:37:28.422  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1592913036=1
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-04 14:37:28.423  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2283032206=1
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2775293581=1
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-04 14:37:28.424  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-04 14:37:28.425  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-04 14:37:28.426  INFO 25232 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-04 14:37:28.528  INFO 25232 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:37:28.528  INFO 25232 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:37:28.595 ERROR 25232 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-04 14:37:28.611  INFO 25232 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:37:28.624  INFO 25232 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-04 14:37:28.639  INFO 25232 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-04 14:37:53.156  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 23308 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 14:37:53.158  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 14:37:53.270  INFO 23308 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 14:37:53.272  INFO 23308 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 14:37:55.225  INFO 23308 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 14:37:55.636  INFO 23308 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 397 ms. Found 15 JPA repository interfaces.
2025-09-04 14:37:56.862  INFO 23308 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 14:37:56.875  INFO 23308 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-04 14:37:56.876  INFO 23308 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-04 14:37:57.223  INFO 23308 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-04 14:37:57.224  INFO 23308 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3951 ms
2025-09-04 14:37:57.663  INFO 23308 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 14:37:57.827  INFO 23308 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 14:37:58.069  INFO 23308 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-04 14:37:58.320  INFO 23308 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-04 14:37:58.535  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-04 14:37:58.540  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-04 14:37:58.543  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-04 14:37:58.545  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-04 14:37:58.549  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-04 14:37:58.553  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-04 14:37:58.556  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-04 14:37:58.559  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-04 14:37:58.562  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-04 14:37:58.565  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-04 14:37:58.568  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-04 14:37:58.572  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-04 14:37:58.577  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-04 14:37:58.579  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-04 14:37:58.581  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-04 14:37:58.584  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-04 14:37:58.586  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-04 14:37:58.588  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-04 14:37:58.591  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-04 14:37:58.593  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-04 14:37:58.595  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-04 14:37:58.597  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-04 14:37:58.599  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-04 14:37:58.603  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-04 14:37:58.605  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-04 14:37:58.607  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-04 14:37:58.609  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-04 14:37:58.612  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-04 14:37:58.615  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-04 14:37:58.617  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-04 14:37:58.651  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-04 14:37:58.653  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-04 14:37:58.655  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-04 14:37:58.657  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-04 14:37:58.662  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-04 14:37:58.664  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-04 14:37:58.665  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-04 14:37:58.668  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-04 14:37:58.670  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-04 14:37:58.672  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-04 14:37:58.675  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-04 14:37:58.676  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-04 14:37:58.678  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-04 14:37:58.681  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-04 14:37:58.683  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-04 14:37:58.686  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-04 14:37:58.688  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-04 14:37:58.689  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-04 14:37:58.694  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-04 14:37:58.696  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-04 14:37:58.699  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-04 14:37:58.702  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-04 14:37:58.713  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-04 14:37:58.716  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-04 14:37:58.718  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-04 14:37:58.720  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-04 14:37:58.722  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-04 14:37:58.862  INFO 23308 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-04 14:37:59.031  INFO 23308 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-04 14:37:59.158  INFO 23308 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 14:37:59.269  INFO 23308 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 14:37:59.497  INFO 23308 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 14:37:59.714  INFO 23308 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 14:38:00.154  INFO 23308 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:38:00.162  INFO 23308 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:38:01.500  INFO 23308 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 14:38:01.515  INFO 23308 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:38:02.386  INFO 23308 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 213 ms
2025-09-04 14:38:03.470  INFO 23308 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 740 ms
2025-09-04 14:38:03.617  INFO 23308 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 14:38:03.681  INFO 23308 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 45 ms.
2025-09-04 14:38:03.830  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 14:38:03.830  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 14:38:03.895  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 65 ms.
2025-09-04 14:38:04.932  WARN 23308 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 14:38:04.932  WARN 23308 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 14:38:05.147  INFO 23308 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@fbbfca7f}
2025-09-04 14:38:06.162  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@379902d6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@f4c94bc0, org.springframework.security.web.context.SecurityContextPersistenceFilter@6214fa72, org.springframework.security.web.header.HeaderWriterFilter@7741acea, org.springframework.security.web.authentication.logout.LogoutFilter@268a74bf, com.deb.spl.control.authorization.AuthenticationFilter@cdd676e7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6a34131c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15ec765, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e0fd1c8f, org.springframework.security.web.session.SessionManagementFilter@6d9ec5f, org.springframework.security.web.access.ExceptionTranslationFilter@1675dcd2, org.springframework.security.web.access.intercept.AuthorizationFilter@640b14c3]
2025-09-04 14:38:06.167  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.167  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-04 14:38:06.168  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.168  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-04 14:38:06.168  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.168  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-04 14:38:06.168  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.168  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-04 14:38:06.169  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.169  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-04 14:38:06.169  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.169  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-04 14:38:06.169  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.169  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-04 14:38:06.169  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.169  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-04 14:38:06.169  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.169  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-04 14:38:06.169  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.169  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-04 14:38:06.170  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.170  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-04 14:38:06.170  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.170  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-04 14:38:06.170  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.170  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-04 14:38:06.170  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.170  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-04 14:38:06.170  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.170  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-04 14:38:06.170  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.170  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-04 14:38:06.171  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.171  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-04 14:38:06.171  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.171  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-04 14:38:06.171  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.171  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-04 14:38:06.172  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.172  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-04 14:38:06.172  WARN 23308 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:38:06.172  INFO 23308 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-04 14:38:06.473  WARN 23308 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-04 14:38:07.312  INFO 23308 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-04 14:38:07.507  INFO 23308 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-04 14:38:07.580  INFO 23308 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-04 14:38:07.580  INFO 23308 --- [ForkJoinPool.commonPool-worker-2] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-04 14:38:07.615  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 15.182 seconds (JVM running for 16.784)
2025-09-04 14:38:07.634  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-04 14:38:07.635  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-04 14:38:07.635  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-04 14:38:07.635  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" "-javaagent:D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar=9896" -Dfile.encoding=UTF-8 -classpath D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar com.deb.spl.control.Application
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_3789132940=1
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1262719628=1
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-04 14:38:07.636  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1592913036=1
2025-09-04 14:38:07.637  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2283032206=1
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2775293581=1
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-04 14:38:07.638  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-04 14:38:07.639  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-04 14:38:07.640  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-04 14:38:07.640  INFO 23308 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-04 14:38:07.687  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:38:07.687  INFO 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:08.390  INFO 23308 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 14:38:08.391  INFO 23308 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-04 14:38:08.394  INFO 23308 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-09-04 14:38:08.611  INFO 23308 --- [http-nio-8079-exec-1] c.vaadin.flow.spring.SpringInstantiator  : The number of beans implementing 'I18NProvider' is 0. Cannot use Spring beans for I18N, falling back to the default behavior
2025-09-04 14:38:08.774 ERROR 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:38:08.780 ERROR 23308 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:38:08.797 ERROR 23308 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:38:08.796165900
2025-09-04 14:38:08.797  INFO 23308 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:08.804 ERROR 23308 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:09.713  INFO 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:09.725 ERROR 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:38:11.367  INFO 23308 --- [ForkJoinPool.commonPool-worker-2] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-04 14:38:11.607 ERROR 23308 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:38:11.607546700
2025-09-04 14:38:13.802  INFO 23308 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:13.808 ERROR 23308 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:14.733 ERROR 23308 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:38:14.733580200
2025-09-04 14:38:17.065 ERROR 23308 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:38:17.065008500
2025-09-04 14:38:18.159  INFO 23308 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:38:18.160  INFO 23308 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 6666 ms
2025-09-04 14:38:18.161  INFO 23308 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:38:18.162  INFO 23308 --- [ForkJoinPool.commonPool-worker-2] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 10580ms
2025-09-04 14:38:18.162  INFO 23308 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:12778/VAADIN/
2025-09-04 14:38:18.948  INFO 23308 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:18.955 ERROR 23308 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:19.730  INFO 23308 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:38:19.730  INFO 23308 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-04 14:38:23.963  INFO 23308 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:23.969 ERROR 23308 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:29.085  INFO 23308 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:29.090 ERROR 23308 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:29.738 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:29.739 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:38:34.090  INFO 23308 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:34.094 ERROR 23308 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:37.617  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:38:39.096 ERROR 23308 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:38:39.097  INFO 23308 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:38:39.100 ERROR 23308 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:58.657 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:38:58.658 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:39:07.609  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:39:09.609  INFO 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:39:09.612 ERROR 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:39:09.613 ERROR 23308 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:39:10.725  INFO 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:39:10.729 ERROR 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:39:28.659 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:39:28.659 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:39:37.612  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:39:39.339 ERROR 23308 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:39:39.339  INFO 23308 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:39:39.342 ERROR 23308 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:39:58.677 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:39:58.677 ERROR 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:40:07.609  INFO 23308 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:40:09.792  INFO 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:09.795 ERROR 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:40:09.795 ERROR 23308 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:40:10.950  INFO 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:10.955 ERROR 23308 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:40:22.700 ERROR 23308 --- [task-6] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-04 14:40:22.724  INFO 23308 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:40:22.732  INFO 23308 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-04 14:40:22.741  INFO 23308 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-04 14:40:28.938  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 17520 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 14:40:28.940  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 14:40:29.053  INFO 17520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 14:40:29.053  INFO 17520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 14:40:31.093  INFO 17520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 14:40:31.421  INFO 17520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 311 ms. Found 15 JPA repository interfaces.
2025-09-04 14:40:32.536  INFO 17520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 14:40:32.550  INFO 17520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-04 14:40:32.551  INFO 17520 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-04 14:40:32.713  INFO 17520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-04 14:40:32.713  INFO 17520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3658 ms
2025-09-04 14:40:33.102  INFO 17520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 14:40:33.266  INFO 17520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 14:40:33.507  INFO 17520 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-04 14:40:33.695  INFO 17520 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-04 14:40:33.864  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-04 14:40:33.868  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-04 14:40:33.872  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-04 14:40:33.876  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-04 14:40:33.880  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-04 14:40:33.883  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-04 14:40:33.886  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-04 14:40:33.888  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-04 14:40:33.891  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-04 14:40:33.892  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-04 14:40:33.896  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-04 14:40:33.899  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-04 14:40:33.902  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-04 14:40:33.904  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-04 14:40:33.907  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-04 14:40:33.909  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-04 14:40:33.912  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-04 14:40:33.915  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-04 14:40:33.917  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-04 14:40:33.919  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-04 14:40:33.922  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-04 14:40:33.924  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-04 14:40:33.928  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-04 14:40:33.930  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-04 14:40:33.933  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-04 14:40:33.935  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-04 14:40:33.938  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-04 14:40:33.941  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-04 14:40:33.947  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-04 14:40:33.951  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-04 14:40:33.990  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-04 14:40:33.995  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-04 14:40:33.997  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-04 14:40:34.002  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-04 14:40:34.004  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-04 14:40:34.007  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-04 14:40:34.010  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-04 14:40:34.012  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-04 14:40:34.014  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-04 14:40:34.015  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-04 14:40:34.018  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-04 14:40:34.020  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-04 14:40:34.024  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-04 14:40:34.026  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-04 14:40:34.028  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-04 14:40:34.029  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-04 14:40:34.032  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-04 14:40:34.034  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-04 14:40:34.037  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-04 14:40:34.038  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-04 14:40:34.041  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-04 14:40:34.043  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-04 14:40:34.053  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-04 14:40:34.055  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-04 14:40:34.058  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-04 14:40:34.061  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-04 14:40:34.063  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-04 14:40:34.189  INFO 17520 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-04 14:40:34.381  INFO 17520 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-04 14:40:34.538  INFO 17520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 14:40:34.683  INFO 17520 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 14:40:34.997  INFO 17520 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 14:40:35.210  INFO 17520 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 14:40:35.717  INFO 17520 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:40:35.726  INFO 17520 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:40:37.432  INFO 17520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 14:40:37.453  INFO 17520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:40:38.379  INFO 17520 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 85 ms
2025-09-04 14:40:39.565  INFO 17520 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 899 ms
2025-09-04 14:40:39.759  INFO 17520 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 14:40:39.840  INFO 17520 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 53 ms.
2025-09-04 14:40:40.001  INFO 17520 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 14:40:40.002  INFO 17520 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 14:40:40.080  INFO 17520 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 78 ms.
2025-09-04 14:40:41.425  WARN 17520 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 14:40:41.425  WARN 17520 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 14:40:41.639  INFO 17520 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@f74846c8}
2025-09-04 14:40:42.859  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@596d8dfb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@c2618073, org.springframework.security.web.context.SecurityContextPersistenceFilter@c3e5670, org.springframework.security.web.header.HeaderWriterFilter@70ea1768, org.springframework.security.web.authentication.logout.LogoutFilter@9fcf4d65, com.deb.spl.control.authorization.AuthenticationFilter@ea200999, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2c5215b2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@52b7c0fa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@dfd8d399, org.springframework.security.web.session.SessionManagementFilter@83613e8e, org.springframework.security.web.access.ExceptionTranslationFilter@e27c98b1, org.springframework.security.web.access.intercept.AuthorizationFilter@a6e2fd79]
2025-09-04 14:40:42.865  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.865  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-04 14:40:42.865  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.866  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-04 14:40:42.866  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.866  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-04 14:40:42.866  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.866  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-04 14:40:42.866  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.866  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-04 14:40:42.866  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.868  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-04 14:40:42.868  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.868  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-04 14:40:42.868  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.868  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-04 14:40:42.868  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.868  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-04 14:40:42.868  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.868  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.869  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.869  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.869  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.869  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.869  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.869  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-04 14:40:42.869  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.870  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-04 14:40:42.870  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.870  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-04 14:40:42.870  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.870  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-04 14:40:42.870  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.870  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-04 14:40:42.871  WARN 17520 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:40:42.871  INFO 17520 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-04 14:40:43.271  WARN 17520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-04 14:40:44.314  INFO 17520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-04 14:40:44.572  INFO 17520 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-04 14:40:44.672  INFO 17520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-04 14:40:44.703  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 16.476 seconds (JVM running for 17.847)
2025-09-04 14:40:44.724  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-04 14:40:44.725  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-04 14:40:44.725  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-04 14:40:44.726  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" "-javaagent:D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar=1853" -Dfile.encoding=UTF-8 -classpath D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar com.deb.spl.control.Application
2025-09-04 14:40:44.726  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-04 14:40:44.726  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_3789132940=1
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1262719628=1
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-04 14:40:44.727  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-04 14:40:44.728  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1592913036=1
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2283032206=1
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2775293581=1
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-04 14:40:44.729  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-04 14:40:44.730  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-04 14:40:44.730  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-04 14:40:44.730  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-04 14:40:44.730  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-04 14:40:44.731  INFO 17520 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-04 14:40:44.760  INFO 17520 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-04 14:40:44.810  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:40:44.811  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:45.595  INFO 17520 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 14:40:45.595  INFO 17520 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-04 14:40:45.599  INFO 17520 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-09-04 14:40:45.948 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:40:45.954 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:40:45.965 ERROR 17520 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:40:45.965717600
2025-09-04 14:40:45.968  INFO 17520 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:45.977 ERROR 17520 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:40:46.902  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:46.911 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:40:48.457  INFO 17520 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-04 14:40:48.806 ERROR 17520 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:40:48.806786300
2025-09-04 14:40:50.967  INFO 17520 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:50.973 ERROR 17520 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:40:51.328 ERROR 17520 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:40:51.328
2025-09-04 14:40:53.763 ERROR 17520 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:40:53.763361
2025-09-04 14:40:54.604  INFO 17520 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:40:54.605  INFO 17520 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 6071 ms
2025-09-04 14:40:54.606  INFO 17520 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:40:54.606  INFO 17520 --- [ForkJoinPool.commonPool-worker-3] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 9845ms
2025-09-04 14:40:54.607  INFO 17520 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:14542/VAADIN/
2025-09-04 14:40:55.977  INFO 17520 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:40:55.982 ERROR 17520 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:40:56.188  INFO 17520 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:40:56.189  INFO 17520 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-04 14:41:01.447  INFO 17520 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:41:01.461 ERROR 17520 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:41:06.452  INFO 17520 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:41:06.464 ERROR 17520 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:41:06.902 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:41:06.903 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:41:11.466  INFO 17520 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:41:11.470 ERROR 17520 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:41:14.873  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:41:16.479  INFO 17520 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:41:16.479 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:41:16.483 ERROR 17520 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:41:35.910 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:41:35.911 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:41:44.710  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:41:46.866  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:41:46.871 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:41:46.871 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:41:47.996  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:41:48.000 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:42:05.739 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:42:05.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:42:14.702  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:42:16.665 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:42:16.666  INFO 17520 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:42:16.670 ERROR 17520 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:42:35.772 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:42:35.773 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:42:44.706  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:42:47.879  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:42:47.883 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:42:47.884 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:42:48.887  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:42:48.892 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:43:05.729 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:43:05.729 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:43:14.702  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:43:16.755 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:43:16.756  INFO 17520 --- [task-1] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:43:16.760 ERROR 17520 --- [task-1] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:43:35.750 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:43:35.751 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:43:44.695  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:43:48.467  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:43:48.470 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:43:48.471 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:43:49.581  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:43:49.584 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:44:05.747 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:44:05.748 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:44:14.699  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:44:16.921 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:44:16.922  INFO 17520 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:44:16.926 ERROR 17520 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:44:35.727 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:44:35.727 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:44:44.718  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:44:48.978  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:44:48.980 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:44:48.982 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:44:49.993  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:44:49.998 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:45:05.758 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:45:05.758 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:45:14.705  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:45:17.056 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:45:17.056  INFO 17520 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:45:17.059 ERROR 17520 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:45:35.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:45:35.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:45:44.700  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:45:49.386  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:45:49.391 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:45:49.391 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:45:50.503  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:45:50.506 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:46:05.735 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:46:05.735 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:46:14.700  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:46:17.234 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:46:17.235  INFO 17520 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:46:17.237 ERROR 17520 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:46:35.751 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:46:35.752 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:46:44.710  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:46:49.980  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:46:49.983 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:46:49.983 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:46:50.997  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:46:51.001 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:47:05.759 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:47:05.759 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:47:14.702  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:47:17.392 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:47:17.393  INFO 17520 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:47:17.395 ERROR 17520 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:47:35.742 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:47:35.743 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:47:44.698  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:47:50.484  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:47:50.487 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:47:50.488 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:47:51.580  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:47:51.584 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:48:05.739 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:48:05.739 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:48:14.695  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:48:17.573 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:48:17.574  INFO 17520 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:48:17.578 ERROR 17520 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:48:35.742 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:48:35.743 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:48:44.696  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:48:51.107  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:48:51.110 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:48:51.110 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:48:52.119  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:48:52.123 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:49:05.730 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:49:05.730 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:49:14.703  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:49:17.667 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:49:17.668  INFO 17520 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:49:17.671 ERROR 17520 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:49:35.775 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:49:35.775 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:49:44.708  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:49:51.914  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:49:51.918 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:49:51.919 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:49:53.013  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:49:53.015 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:50:05.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:50:05.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:50:14.700  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:50:17.758 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:50:17.758  INFO 17520 --- [task-4] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:50:17.762 ERROR 17520 --- [task-4] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:50:35.757 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:50:35.757 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:50:44.698  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:50:52.769  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:50:52.772 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:50:52.772 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:50:53.784  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:50:53.786 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:51:05.734 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:51:05.734 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:51:14.708  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:51:17.934 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:51:17.935  INFO 17520 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:51:17.938 ERROR 17520 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:51:35.761 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:51:35.761 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:51:44.704  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:51:53.311  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:51:53.315 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:51:53.316 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:51:54.437  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:51:54.440 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:52:05.757 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:52:05.757 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:52:14.708  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:52:18.110 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:52:18.110  INFO 17520 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:52:18.114 ERROR 17520 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:52:35.748 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:52:35.748 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:52:44.709  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:52:53.940  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:52:53.943 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:52:53.943 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:52:54.953  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:52:54.956 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:53:05.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:53:05.740 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:53:14.710  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:53:18.278 ERROR 17520 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-04 14:53:18.279  INFO 17520 --- [task-3] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:53:18.282 ERROR 17520 --- [task-3] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:53:35.774 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:53:35.774 ERROR 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:53:44.702  INFO 17520 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:53:54.495  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:53:54.499 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:53:54.500 ERROR 17520 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:53:55.599  INFO 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:53:55.601 ERROR 17520 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:54:03.267 ERROR 17520 --- [task-7] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-04 14:54:03.283  INFO 17520 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:54:03.291  INFO 17520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-04 14:54:03.298  INFO 17520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-04 14:54:12.103  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 19968 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 14:54:12.107  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 14:54:12.350  INFO 19968 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 14:54:12.351  INFO 19968 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 14:54:14.820  INFO 19968 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 14:54:15.469  INFO 19968 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 628 ms. Found 15 JPA repository interfaces.
2025-09-04 14:54:17.195  INFO 19968 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 14:54:17.212  INFO 19968 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-04 14:54:17.213  INFO 19968 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-04 14:54:17.400  INFO 19968 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-04 14:54:17.401  INFO 19968 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5047 ms
2025-09-04 14:54:17.935  INFO 19968 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 14:54:18.226  INFO 19968 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 14:54:18.551  INFO 19968 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-04 14:54:18.752  INFO 19968 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-04 14:54:19.015  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-04 14:54:19.021  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-04 14:54:19.024  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-04 14:54:19.028  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-04 14:54:19.033  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-04 14:54:19.037  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-04 14:54:19.040  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-04 14:54:19.045  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-04 14:54:19.048  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-04 14:54:19.050  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-04 14:54:19.053  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-04 14:54:19.056  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-04 14:54:19.063  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-04 14:54:19.065  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-04 14:54:19.070  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-04 14:54:19.078  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-04 14:54:19.080  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-04 14:54:19.083  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-04 14:54:19.086  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-04 14:54:19.089  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-04 14:54:19.092  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-04 14:54:19.095  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-04 14:54:19.099  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-04 14:54:19.102  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-04 14:54:19.104  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-04 14:54:19.107  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-04 14:54:19.109  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-04 14:54:19.113  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-04 14:54:19.117  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-04 14:54:19.125  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-04 14:54:19.193  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-04 14:54:19.200  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-04 14:54:19.203  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-04 14:54:19.211  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-04 14:54:19.217  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-04 14:54:19.221  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-04 14:54:19.224  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-04 14:54:19.226  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-04 14:54:19.229  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-04 14:54:19.234  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-04 14:54:19.236  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-04 14:54:19.240  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-04 14:54:19.243  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-04 14:54:19.247  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-04 14:54:19.249  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-04 14:54:19.251  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-04 14:54:19.254  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-04 14:54:19.258  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-04 14:54:19.262  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-04 14:54:19.264  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-04 14:54:19.266  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-04 14:54:19.269  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-04 14:54:19.289  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-04 14:54:19.293  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-04 14:54:19.296  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-04 14:54:19.298  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-04 14:54:19.299  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-04 14:54:19.453  INFO 19968 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-04 14:54:19.655  INFO 19968 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-04 14:54:19.808  INFO 19968 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 14:54:19.909  INFO 19968 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 14:54:20.340  INFO 19968 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 14:54:20.655  INFO 19968 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 14:54:21.297  INFO 19968 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:54:21.305  INFO 19968 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:54:23.430  INFO 19968 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 14:54:23.451  INFO 19968 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:54:24.485  INFO 19968 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 102 ms
2025-09-04 14:54:26.027  INFO 19968 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1121 ms
2025-09-04 14:54:26.359  INFO 19968 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 14:54:26.462  INFO 19968 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 70 ms.
2025-09-04 14:54:26.678  INFO 19968 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 14:54:26.679  INFO 19968 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 14:54:26.792  INFO 19968 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 112 ms.
2025-09-04 14:54:28.359  WARN 19968 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 14:54:28.359  WARN 19968 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 14:54:28.627  INFO 19968 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@7d3b44a9}
2025-09-04 14:54:30.162  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ef1a5abd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@9480084c, org.springframework.security.web.context.SecurityContextPersistenceFilter@cd01cb1e, org.springframework.security.web.header.HeaderWriterFilter@c1d5627c, org.springframework.security.web.authentication.logout.LogoutFilter@9d83fdde, com.deb.spl.control.authorization.AuthenticationFilter@871704c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30827024, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@315e0b7b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f112b8a, org.springframework.security.web.session.SessionManagementFilter@66cce69, org.springframework.security.web.access.ExceptionTranslationFilter@ec6d14a1, org.springframework.security.web.access.intercept.AuthorizationFilter@706be6ab]
2025-09-04 14:54:30.169  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.170  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-04 14:54:30.172  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.172  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-04 14:54:30.173  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.173  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-04 14:54:30.175  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.175  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-04 14:54:30.175  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.175  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-04 14:54:30.175  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.175  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-04 14:54:30.175  WARN 19968 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:54:30.175  INFO 19968 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-04 14:54:30.579  WARN 19968 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-04 14:54:31.851  INFO 19968 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-04 14:54:32.140  INFO 19968 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-04 14:54:32.249  INFO 19968 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-04 14:54:32.256  INFO 19968 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-04 14:54:34.981  INFO 19968 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-04 14:54:36.060  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 26.93 seconds (JVM running for 29.699)
2025-09-04 14:54:36.147  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-04 14:54:36.148  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-04 14:54:36.148  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-04 14:54:36.149  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:9459,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture12907258330493031182.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-04 14:54:36.150  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-04 14:54:36.150  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-04 14:54:36.150  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-04 14:54:36.150  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-04 14:54:36.150  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-04 14:54:36.150  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-04 14:54:36.151  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-04 14:54:36.151  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_3789132940=1
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1262719628=1
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-04 14:54:36.152  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-04 14:54:36.153  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-04 14:54:36.153  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-04 14:54:36.153  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-04 14:54:36.153  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-04 14:54:36.153  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-04 14:54:36.153  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-04 14:54:36.154  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-04 14:54:36.155  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1592913036=1
2025-09-04 14:54:36.156  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-04 14:54:36.162  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-04 14:54:36.165  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-04 14:54:36.166  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-04 14:54:36.166  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-04 14:54:36.166  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-04 14:54:36.166  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2283032206=1
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2775293581=1
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-04 14:54:36.167  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-04 14:54:36.168  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-04 14:54:36.168  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-04 14:54:36.168  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-04 14:54:36.168  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-04 14:54:36.168  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-04 14:54:36.168  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-04 14:54:36.169  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-04 14:56:59.983  INFO 19968 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:56:59.986  WARN 19968 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m41s652ms913µs400ns).
2025-09-04 14:56:59.986  INFO 19968 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 3431 ms
2025-09-04 14:56:59.996  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-04 14:56:59.998  INFO 19968 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 147742ms
2025-09-04 14:57:00.001  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-04 14:57:00.001  INFO 19968 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:57:00.001  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-04 14:57:00.001  INFO 19968 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-04 14:57:00.001  INFO 19968 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:8584/VAADIN/
2025-09-04 14:57:00.001  INFO 19968 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:57:00.002  INFO 19968 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-04 14:57:00.002 ERROR 19968 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1960), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2095), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-04 14:57:00.112  INFO 19968 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:57:00.112  INFO 19968 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:57:00.340  INFO 19968 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:57:00.358  INFO 19968 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-04 14:57:00.374  INFO 19968 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-04 14:58:00.253  INFO 18580 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 18580 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 14:58:00.258  INFO 18580 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 14:58:00.464  INFO 18580 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 14:58:00.464  INFO 18580 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 14:58:03.297  INFO 18580 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 14:58:03.711  INFO 18580 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 391 ms. Found 15 JPA repository interfaces.
2025-09-04 14:58:05.364  INFO 18580 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 14:58:05.382  INFO 18580 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-04 14:58:05.383  INFO 18580 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-04 14:58:05.574  INFO 18580 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-04 14:58:05.575  INFO 18580 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5110 ms
2025-09-04 14:58:06.244  INFO 18580 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 14:58:06.519  INFO 18580 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 14:58:08.155  INFO 18580 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 14:58:08.251  INFO 18580 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 14:58:08.511  INFO 18580 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 14:58:08.768  INFO 18580 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 14:58:09.511  INFO 18580 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:58:09.520  INFO 18580 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:58:12.895  INFO 18580 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 14:58:12.938  INFO 18580 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:58:14.278  INFO 18580 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 178 ms
2025-09-04 14:58:16.409  INFO 18580 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1501 ms
2025-09-04 14:58:16.735  INFO 18580 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 14:58:16.875  INFO 18580 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 77 ms.
2025-09-04 14:58:17.056  INFO 18580 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 14:58:17.057  INFO 18580 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 14:58:17.147  INFO 18580 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 89 ms.
2025-09-04 14:58:18.566  WARN 18580 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 14:58:18.567  WARN 18580 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 14:58:18.850  INFO 18580 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@519342a8}
2025-09-04 14:58:49.233  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 24348 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 14:58:49.237  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 14:58:49.941  INFO 24348 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 14:58:49.941  INFO 24348 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 14:58:52.873  INFO 24348 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 14:58:53.364  INFO 24348 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 468 ms. Found 15 JPA repository interfaces.
2025-09-04 14:58:54.924  INFO 24348 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 14:58:54.940  INFO 24348 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-04 14:58:54.940  INFO 24348 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-04 14:58:55.143  INFO 24348 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-04 14:58:55.144  INFO 24348 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5201 ms
2025-09-04 14:58:55.683  INFO 24348 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 14:58:55.969  INFO 24348 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 14:58:56.299  INFO 24348 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-04 14:58:56.510  INFO 24348 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-04 14:58:56.778  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-04 14:58:56.786  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-04 14:58:56.789  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-04 14:58:56.792  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-04 14:58:56.796  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-04 14:58:56.800  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-04 14:58:56.802  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-04 14:58:56.805  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-04 14:58:56.807  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-04 14:58:56.810  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-04 14:58:56.813  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-04 14:58:56.815  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-04 14:58:56.818  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-04 14:58:56.820  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-04 14:58:56.823  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-04 14:58:56.826  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-04 14:58:56.830  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-04 14:58:56.833  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-04 14:58:56.836  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-04 14:58:56.837  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-04 14:58:56.840  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-04 14:58:56.843  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-04 14:58:56.846  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-04 14:58:56.849  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-04 14:58:56.851  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-04 14:58:56.853  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-04 14:58:56.855  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-04 14:58:56.857  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-04 14:58:56.862  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-04 14:58:56.864  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-04 14:58:56.930  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-04 14:58:56.933  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-04 14:58:56.937  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-04 14:58:56.941  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-04 14:58:56.946  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-04 14:58:56.948  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-04 14:58:56.951  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-04 14:58:56.954  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-04 14:58:56.956  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-04 14:58:56.961  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-04 14:58:56.964  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-04 14:58:56.966  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-04 14:58:56.968  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-04 14:58:56.970  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-04 14:58:56.972  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-04 14:58:56.976  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-04 14:58:56.978  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-04 14:58:56.980  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-04 14:58:56.982  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-04 14:58:56.985  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-04 14:58:56.987  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-04 14:58:56.990  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-04 14:58:57.008  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-04 14:58:57.011  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-04 14:58:57.013  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-04 14:58:57.016  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-04 14:58:57.018  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-04 14:58:57.268  INFO 24348 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-04 14:58:57.615  INFO 24348 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-04 14:58:57.790  INFO 24348 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 14:58:57.942  INFO 24348 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 14:58:58.307  INFO 24348 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 14:58:58.603  INFO 24348 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 14:58:59.329  INFO 24348 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:58:59.344  INFO 24348 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 14:59:01.465  INFO 24348 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 14:59:01.505  INFO 24348 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 14:59:02.558  INFO 24348 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 111 ms
2025-09-04 14:59:04.094  INFO 24348 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1130 ms
2025-09-04 14:59:04.388  INFO 24348 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 14:59:04.482  INFO 24348 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 60 ms.
2025-09-04 14:59:04.689  INFO 24348 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 14:59:04.689  INFO 24348 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 14:59:04.806  INFO 24348 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 117 ms.
2025-09-04 14:59:06.273  WARN 24348 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 14:59:06.274  WARN 24348 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 14:59:06.542  INFO 24348 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@b193fa5b}
2025-09-04 14:59:08.089  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@49167145, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@9238c5da, org.springframework.security.web.context.SecurityContextPersistenceFilter@af8bea9a, org.springframework.security.web.header.HeaderWriterFilter@dde2e243, org.springframework.security.web.authentication.logout.LogoutFilter@66086595, com.deb.spl.control.authorization.AuthenticationFilter@d947e2e4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a6a8f32c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@992de388, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e121d3ba, org.springframework.security.web.session.SessionManagementFilter@d831f080, org.springframework.security.web.access.ExceptionTranslationFilter@5d31d567, org.springframework.security.web.access.intercept.AuthorizationFilter@71680170]
2025-09-04 14:59:08.098  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.100  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-04 14:59:08.101  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.101  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-04 14:59:08.102  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.102  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-04 14:59:08.102  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.102  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-04 14:59:08.102  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.102  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-04 14:59:08.102  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.102  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-04 14:59:08.103  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.103  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-04 14:59:08.103  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.103  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-04 14:59:08.103  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.103  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-04 14:59:08.103  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.104  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-04 14:59:08.104  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.104  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-04 14:59:08.104  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.104  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-04 14:59:08.104  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.104  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-04 14:59:08.104  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.104  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-04 14:59:08.104  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.104  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-04 14:59:08.105  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.105  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-04 14:59:08.105  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.105  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-04 14:59:08.105  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.105  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-04 14:59:08.105  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.106  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-04 14:59:08.106  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.106  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-04 14:59:08.106  WARN 24348 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 14:59:08.106  INFO 24348 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-04 14:59:08.508  WARN 24348 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-04 14:59:09.677  INFO 24348 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-04 14:59:10.089  INFO 24348 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-04 14:59:10.238  INFO 24348 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-04 14:59:10.277  INFO 24348 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-04 14:59:10.304  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 22.212 seconds (JVM running for 46.288)
2025-09-04 14:59:10.341  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-04 14:59:10.342  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:4178,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture13416225441630426038.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-04 14:59:10.343  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_3789132940=1
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1262719628=1
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-04 14:59:10.344  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-04 14:59:10.345  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-04 14:59:10.346  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1592913036=1
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2283032206=1
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2775293581=1
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-04 14:59:10.347  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-04 14:59:10.348  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-04 14:59:10.348  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-04 14:59:10.348  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-04 14:59:10.348  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-04 14:59:10.348  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-04 14:59:10.349  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-04 14:59:10.351  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-04 14:59:10.351  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-04 14:59:10.351  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-04 14:59:10.352  INFO 24348 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-04 14:59:10.455  INFO 24348 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:10.455  INFO 24348 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 14:59:11.594  INFO 24348 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 14:59:11.595  INFO 24348 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-04 14:59:11.598  INFO 24348 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-09-04 14:59:11.868  INFO 24348 --- [http-nio-8079-exec-1] c.vaadin.flow.spring.SpringInstantiator  : The number of beans implementing 'I18NProvider' is 0. Cannot use Spring beans for I18N, falling back to the default behavior
2025-09-04 14:59:12.144 ERROR 24348 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-04 14:59:12.152 ERROR 24348 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-04 14:59:12.167 ERROR 24348 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:59:12.167524
2025-09-04 14:59:12.171  INFO 24348 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:12.182 ERROR 24348 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:59:13.326  INFO 24348 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:13.339 ERROR 24348 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-04 14:59:14.694  INFO 24348 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-04 14:59:15.213 ERROR 24348 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:59:15.213603200
2025-09-04 14:59:17.173  INFO 24348 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:17.187 ERROR 24348 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:59:18.311 ERROR 24348 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:59:18.311651300
2025-09-04 14:59:20.840 ERROR 24348 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-04T14:59:20.840035300
2025-09-04 14:59:20.996  INFO 24348 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:59:20.998  INFO 24348 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 6165 ms
2025-09-04 14:59:20.999  INFO 24348 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:59:21.000  INFO 24348 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:14115/VAADIN/
2025-09-04 14:59:21.003  INFO 24348 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 10725ms
2025-09-04 14:59:22.179  INFO 24348 --- [task-7] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:22.191 ERROR 24348 --- [task-7] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:59:23.053  INFO 24348 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 14:59:23.054  INFO 24348 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-04 14:59:27.185  INFO 24348 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:27.194 ERROR 24348 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:59:32.189  INFO 24348 --- [task-6] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:32.196 ERROR 24348 --- [task-6] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:59:33.031 ERROR 24348 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 14:59:33.032 ERROR 24348 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-04 14:59:37.191  INFO 24348 --- [task-5] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 14:59:37.215 ERROR 24348 --- [task-5] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-04 15:00:30.271  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 20512 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-04 15:00:30.275  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-04 15:00:30.542  INFO 20512 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-04 15:00:30.543  INFO 20512 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-04 15:00:33.127  INFO 20512 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-04 15:00:33.757  INFO 20512 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 608 ms. Found 15 JPA repository interfaces.
2025-09-04 15:00:35.325  INFO 20512 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-04 15:00:35.341  INFO 20512 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-04 15:00:35.341  INFO 20512 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-04 15:00:35.527  INFO 20512 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-04 15:00:35.528  INFO 20512 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4983 ms
2025-09-04 15:00:36.102  INFO 20512 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-04 15:00:36.395  INFO 20512 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-04 15:00:36.708  INFO 20512 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-04 15:00:36.954  INFO 20512 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-04 15:00:37.203  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-04 15:00:37.207  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-04 15:00:37.210  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-04 15:00:37.213  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-04 15:00:37.216  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-04 15:00:37.220  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-04 15:00:37.222  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-04 15:00:37.225  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-04 15:00:37.227  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-04 15:00:37.229  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-04 15:00:37.232  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-04 15:00:37.234  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-04 15:00:37.238  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-04 15:00:37.240  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-04 15:00:37.243  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-04 15:00:37.249  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-04 15:00:37.254  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-04 15:00:37.256  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-04 15:00:37.259  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-04 15:00:37.261  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-04 15:00:37.264  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-04 15:00:37.267  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-04 15:00:37.269  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-04 15:00:37.272  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-04 15:00:37.275  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-04 15:00:37.277  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-04 15:00:37.279  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-04 15:00:37.282  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-04 15:00:37.285  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-04 15:00:37.287  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-04 15:00:37.343  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-04 15:00:37.346  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-04 15:00:37.348  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-04 15:00:37.350  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-04 15:00:37.353  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-04 15:00:37.355  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-04 15:00:37.358  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-04 15:00:37.359  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-04 15:00:37.363  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-04 15:00:37.365  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-04 15:00:37.366  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-04 15:00:37.368  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-04 15:00:37.371  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-04 15:00:37.374  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-04 15:00:37.375  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-04 15:00:37.380  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-04 15:00:37.383  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-04 15:00:37.385  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-04 15:00:37.388  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-04 15:00:37.389  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-04 15:00:37.392  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-04 15:00:37.394  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-04 15:00:37.407  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-04 15:00:37.409  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-04 15:00:37.411  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-04 15:00:37.414  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-04 15:00:37.416  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-04 15:00:37.572  INFO 20512 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-04 15:00:37.763  INFO 20512 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-04 15:00:37.909  INFO 20512 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-04 15:00:38.006  INFO 20512 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-04 15:00:38.319  INFO 20512 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-04 15:00:38.600  INFO 20512 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-04 15:00:39.210  INFO 20512 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 15:00:39.219  INFO 20512 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-04 15:00:41.348  INFO 20512 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-04 15:00:41.376  INFO 20512 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 15:00:42.440  INFO 20512 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 103 ms
2025-09-04 15:00:43.882  INFO 20512 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1016 ms
2025-09-04 15:00:44.131  INFO 20512 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-04 15:00:44.210  INFO 20512 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 56 ms.
2025-09-04 15:00:44.379  INFO 20512 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-04 15:00:44.379  INFO 20512 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-04 15:00:44.470  INFO 20512 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 91 ms.
2025-09-04 15:00:46.113  WARN 20512 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Номерний знак СПУ змінено з AA 0000 AA на DP 0101 UA
2025-09-04 15:00:46.114  WARN 20512 --- [restartedMain] c.d.s.control.service.asku.AskuService   : Кодову назву СПУ змінено з ??? на spl101
2025-09-04 15:00:46.472  INFO 20512 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=1001, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=null, updatedAt=2025-08-29T16:40:56.829075, splReadiness=ТГ № 4, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1, dateReleaseM=2023-07-21T15:08:25, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1, createdAt=2023-07-21T15:07:13, plantMissile='1Л', warhead=MFBCH, gsnType=NO_GSN, alpType=FOUR_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='""'}, initialData=LaunchInitialDataDao{id=1, createdAt=2023-07-21T14:57:54, loadTemperature=-999.9, latitudeRad=0.9730396216435608, longitudeRad=0.6565960162985366, altitude=101.11, inclinationAngle=-86.0, trajectory=BALLISTIC, readiness=БГ № 1, isProDetected=false, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=false, startTimenull'}null, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='false', storedTlKeys={werwer23443dasdasdasdas234rrrwsssdfgdasd===;0123465798/*--!@#$%^&fsf3wffffffffffffffff===}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@e82e0082}
2025-09-04 15:00:48.056  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@110aedb3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a6184bd9, org.springframework.security.web.context.SecurityContextPersistenceFilter@ba5df4c0, org.springframework.security.web.header.HeaderWriterFilter@a18b3c81, org.springframework.security.web.authentication.logout.LogoutFilter@5576b670, com.deb.spl.control.authorization.AuthenticationFilter@9e1bab59, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@551e5dc3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@276290d0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7a7fc464, org.springframework.security.web.session.SessionManagementFilter@66436101, org.springframework.security.web.access.ExceptionTranslationFilter@2cb06020, org.springframework.security.web.access.intercept.AuthorizationFilter@d30beb75]
2025-09-04 15:00:48.066  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.067  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-04 15:00:48.069  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-04 15:00:48.070  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-04 15:00:48.070  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-04 15:00:48.070  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-04 15:00:48.070  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-04 15:00:48.070  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-04 15:00:48.070  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.070  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-04 15:00:48.071  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.071  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-04 15:00:48.071  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.071  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.072  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.072  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.072  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.072  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.072  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.072  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-04 15:00:48.072  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.073  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-04 15:00:48.073  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.073  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-04 15:00:48.073  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.074  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-04 15:00:48.074  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.074  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-04 15:00:48.074  WARN 20512 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-04 15:00:48.074  INFO 20512 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-04 15:00:48.482  WARN 20512 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-04 15:00:49.760  INFO 20512 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-04 15:00:50.037  INFO 20512 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-04 15:00:50.145  INFO 20512 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-04 15:00:50.151  INFO 20512 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-04 15:00:53.125  INFO 20512 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-04 15:00:53.735  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 25.468 seconds (JVM running for 27.62)
2025-09-04 15:00:53.788  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-04 15:00:53.790  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-04 15:00:53.791  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-04 15:00:53.792  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:7965,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture10799592496634644872.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-04 15:00:53.793  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-04 15:00:53.794  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-04 15:00:53.795  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-04 15:00:53.796  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-04 15:00:53.797  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-04 15:00:53.798  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-04 15:00:53.799  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-04 15:00:53.799  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-04 15:00:53.799  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_3789132940=1
2025-09-04 15:00:53.799  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-04 15:00:53.799  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1262719628=1
2025-09-04 15:00:53.801  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-04 15:00:53.801  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-04 15:00:53.801  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-04 15:00:53.801  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-04 15:00:53.801  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-04 15:00:53.801  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-04 15:00:53.802  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_1592913036=1
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2283032206=1
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : EFC_10100_2775293581=1
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-04 15:00:53.804  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-04 15:00:53.805  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-04 15:00:53.806  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-04 15:00:53.806  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-04 15:00:53.810  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-04 15:00:53.814  INFO 20512 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-04 15:00:53.929  INFO 20512 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-04 15:00:53.933  INFO 20512 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-04 15:04:33.414  WARN 20512 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m56s904ms5µs600ns).
2025-09-04 15:04:33.414  INFO 20512 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 15:04:33.416  INFO 20512 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 4781 ms
2025-09-04 15:04:33.427  INFO 20512 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 15:04:33.427 ERROR 20512 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1960), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2095), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-04 15:04:33.429  INFO 20512 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:2354/VAADIN/
2025-09-04 15:04:33.430  INFO 20512 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-04 15:04:33.430  INFO 20512 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-04 15:04:33.431  INFO 20512 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 223278ms
2025-09-04 15:04:33.855  INFO 20512 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-04 15:04:33.878  INFO 20512 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-04 15:04:33.898  INFO 20512 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
