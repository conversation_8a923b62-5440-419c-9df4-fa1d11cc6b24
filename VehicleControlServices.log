2025-09-16 12:57:23.497  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 22344 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-16 12:57:23.499  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-16 12:57:23.742  INFO 22344 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-16 12:57:23.743  INFO 22344 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-16 12:57:33.036  INFO 22344 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16 12:57:33.977  INFO 22344 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 915 ms. Found 15 JPA repository interfaces.
2025-09-16 12:57:36.056  INFO 22344 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-16 12:57:36.076  INFO 22344 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-16 12:57:36.077  INFO 22344 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-16 12:57:36.328  INFO 22344 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-16 12:57:36.329  INFO 22344 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 12582 ms
2025-09-16 12:57:37.066  INFO 22344 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-16 12:57:37.411  INFO 22344 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-16 12:57:37.823  INFO 22344 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-16 12:57:38.066  INFO 22344 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-16 12:57:38.465  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-16 12:57:38.483  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-16 12:57:38.495  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-16 12:57:38.507  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-16 12:57:38.529  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-16 12:57:38.549  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-16 12:57:38.562  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-16 12:57:38.578  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-16 12:57:38.593  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-16 12:57:38.608  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-16 12:57:38.621  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-16 12:57:38.637  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-16 12:57:38.656  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-16 12:57:38.668  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-16 12:57:38.693  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-16 12:57:38.717  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-16 12:57:38.736  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-16 12:57:38.754  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-16 12:57:38.773  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-16 12:57:38.791  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-16 12:57:38.805  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-16 12:57:38.820  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-16 12:57:38.841  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-16 12:57:38.856  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-16 12:57:38.869  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-16 12:57:38.881  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-16 12:57:38.892  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-16 12:57:38.906  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-16 12:57:38.932  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-16 12:57:38.947  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-16 12:57:39.048  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-16 12:57:39.065  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-16 12:57:39.079  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-16 12:57:39.097  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-16 12:57:39.118  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-16 12:57:39.132  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-16 12:57:39.150  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-16 12:57:39.167  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-16 12:57:39.188  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-16 12:57:39.208  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-16 12:57:39.235  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-16 12:57:39.248  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-16 12:57:39.266  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-16 12:57:39.280  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-16 12:57:39.299  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-16 12:57:39.312  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-16 12:57:39.328  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-16 12:57:39.341  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-16 12:57:39.371  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-16 12:57:39.389  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-16 12:57:39.409  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-16 12:57:39.424  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-16 12:57:39.458  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-16 12:57:39.476  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-16 12:57:39.491  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-16 12:57:39.510  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-16 12:57:39.522  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-16 12:57:39.768  INFO 22344 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-16 12:57:40.062  INFO 22344 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-16 12:57:40.250  INFO 22344 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16 12:57:40.422  INFO 22344 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-16 12:57:40.876  INFO 22344 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-16 12:57:41.495  INFO 22344 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-16 12:57:42.289  INFO 22344 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 12:57:42.312  INFO 22344 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 12:57:45.854  INFO 22344 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-16 12:57:45.885  INFO 22344 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 12:57:47.410  INFO 22344 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 168 ms
2025-09-16 12:57:49.406  INFO 22344 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1508 ms
2025-09-16 12:57:49.791  INFO 22344 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-16 12:57:49.885  INFO 22344 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 69 ms.
2025-09-16 12:57:50.265  INFO 22344 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-16 12:57:50.266  INFO 22344 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-16 12:57:51.425  INFO 22344 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 1159 ms.
2025-09-16 12:57:52.319  INFO 22344 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=6894, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=2025-09-15T23:17:55.899497, updatedAt=2025-09-16T11:27:22.742881, splReadiness=БГ № 2, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1124, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1032, createdAt=2025-09-16T11:11:12.028664, plantMissile='11', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=true, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=LaunchInitialDataDao{id=1030, createdAt=2025-09-16T11:11:12.676411, loadTemperature=-999.9, latitudeRad=45.12877000, longitudeRad=31.67633000, altitude=0.0, inclinationAngle=-75.0, trajectory=AERO_BALLISTIC, readiness=БГ № 2, isProDetected=true, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=true, startTime2025-09-16T11:41:12.570784'}OrderInfoDao{id=24, orderEntityId=aa29b624-0e2b-4687-a2e6-b268422cdb11, validUntil=2025-09-16T12:11:12.570784}, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='true', storedTlKeys={123;123---sdfsdfsfd1465ssdfsd====}
, sensorTemperature=15.6, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@d307f04e}
2025-09-16 13:03:47.583  WARN 22344 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m10s68ms574µs300ns).
2025-09-16 13:03:48.923  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@b9691569, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4da5f2e7, org.springframework.security.web.context.SecurityContextPersistenceFilter@7034bd1b, org.springframework.security.web.header.HeaderWriterFilter@9ab1fddc, org.springframework.security.web.authentication.logout.LogoutFilter@d74d27c2, com.deb.spl.control.authorization.AuthenticationFilter@bebd731d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a751241f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2cd44124, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3c5b96f1, org.springframework.security.web.session.SessionManagementFilter@17c62f08, org.springframework.security.web.access.ExceptionTranslationFilter@cc05b796, org.springframework.security.web.access.intercept.AuthorizationFilter@751fc695]
2025-09-16 13:03:48.929  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.930  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-16 13:03:48.931  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.931  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-16 13:03:48.931  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.931  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-16 13:03:48.931  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.931  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-16 13:03:48.931  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.931  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-16 13:03:48.932  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.932  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-16 13:03:48.932  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.932  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-16 13:03:48.932  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.932  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-16 13:03:48.932  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.932  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-16 13:03:48.932  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.932  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.933  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-16 13:03:48.933  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.935  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-16 13:03:48.935  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.935  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-16 13:03:48.935  WARN 22344 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:03:48.935  INFO 22344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-16 13:03:49.426  WARN 22344 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-16 13:03:50.571  INFO 22344 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-16 13:03:50.782 ERROR 22344 --- [restartedMain] org.atmosphere.cpr.AtmosphereFramework   : Failed to initialize Atmosphere Framework

java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.initAtmosphereForVaadinServlet(JSR356WebsocketInitializer.java:186) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.init(JSR356WebsocketInitializer.java:151) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.VaadinWebsocketEndpointExporter.registerEndpoints(VaadinWebsocketEndpointExporter.java:51) ~[vaadin-spring-23.3.3.jar:na]
	at org.springframework.web.socket.server.standard.ServerEndpointExporter.afterSingletonsInstantiated(ServerEndpointExporter.java:112) ~[spring-websocket-5.3.25.jar:5.3.25]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974) ~[spring-beans-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:43) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]

2025-09-16 13:03:50.785  WARN 22344 --- [restartedMain] c.v.f.s.c.JSR356WebsocketInitializer     : Failed to initialize Atmosphere for springServlet

java.lang.RuntimeException: Atmosphere init failed
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:253) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.initAtmosphereForVaadinServlet(JSR356WebsocketInitializer.java:186) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.JSR356WebsocketInitializer.init(JSR356WebsocketInitializer.java:151) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.VaadinWebsocketEndpointExporter.registerEndpoints(VaadinWebsocketEndpointExporter.java:51) ~[vaadin-spring-23.3.3.jar:na]
	at org.springframework.web.socket.server.standard.ServerEndpointExporter.afterSingletonsInstantiated(ServerEndpointExporter.java:112) ~[spring-websocket-5.3.25.jar:5.3.25]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:974) ~[spring-beans-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:43) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]
Caused by: javax.servlet.ServletException: java.lang.IllegalStateException: Shutdown in progress
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:946) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	... 19 common frames omitted
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	... 21 common frames omitted

2025-09-16 13:03:50.869  INFO 22344 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-16 13:03:50.950 ERROR 22344 --- [restartedMain] org.atmosphere.cpr.AtmosphereFramework   : Failed to initialize Atmosphere Framework

java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.3.jar:23.3.3]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8.jar:2.7.8]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:522) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:512) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:239) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:43) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]

2025-09-16 13:03:50.953  WARN 22344 --- [restartedMain] c.v.flow.server.VaadinServletService     : Error initializing Atmosphere. Push will not work.

com.vaadin.flow.server.ServiceException: Failed to initialize Atmosphere for springServlet. Push will not work.
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:100) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.createRequestHandlers(VaadinServletService.java:107) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinService.init(VaadinService.java:231) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServletService.init(VaadinServletService.java:175) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.spring.SpringVaadinServletService.init(SpringVaadinServletService.java:103) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.spring.SpringServlet.createServletService(SpringServlet.java:115) ~[vaadin-spring-23.3.3.jar:na]
	at com.vaadin.flow.server.VaadinServlet.createServletService(VaadinServlet.java:307) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.VaadinServlet.init(VaadinServlet.java:130) ~[flow-server-23.3.3.jar:23.3.3]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1161) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:1010) ~[tomcat-embed-core-9.0.71.jar:9.0.71]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.load(TomcatEmbeddedContext.java:81) ~[spring-boot-2.7.8.jar:2.7.8]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$7$1.accept(ReferencePipeline.java:276) ~[na:na]
	at java.base/java.util.TreeMap$ValueSpliterator.forEachRemaining(TreeMap.java:3215) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:522) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:512) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:239) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.lambda$deferredLoadOnStartup$0(TomcatEmbeddedContext.java:64) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.doWithThreadContextClassLoader(TomcatEmbeddedContext.java:105) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedContext.deferredLoadOnStartup(TomcatEmbeddedContext.java:63) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.performDeferredLoadOnStartup(TomcatWebServer.java:305) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:216) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:43) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356) ~[spring-context-5.3.25.jar:5.3.25]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303) ~[spring-boot-2.7.8.jar:2.7.8]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292) ~[spring-boot-2.7.8.jar:2.7.8]
	at com.deb.spl.control.Application.main(Application.java:43) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49) ~[spring-boot-devtools-2.7.8.jar:2.7.8]
Caused by: java.lang.RuntimeException: Atmosphere init failed
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:253) ~[flow-server-23.3.3.jar:23.3.3]
	at com.vaadin.flow.server.communication.PushRequestHandler.<init>(PushRequestHandler.java:96) ~[flow-server-23.3.3.jar:23.3.3]
	... 47 common frames omitted
Caused by: javax.servlet.ServletException: java.lang.IllegalStateException: Shutdown in progress
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:946) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:833) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	at com.vaadin.flow.server.communication.PushRequestHandler.initAtmosphere(PushRequestHandler.java:244) ~[flow-server-23.3.3.jar:23.3.3]
	... 48 common frames omitted
Caused by: java.lang.IllegalStateException: Shutdown in progress
	at java.base/java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66) ~[na:na]
	at java.base/java.lang.Runtime.addShutdownHook(Runtime.java:216) ~[na:na]
	at org.atmosphere.cpr.AtmosphereFramework.init(AtmosphereFramework.java:928) ~[atmosphere-runtime-2.7.3.slf4jvaadin4.jar:na]
	... 50 common frames omitted

2025-09-16 13:03:50.983  INFO 22344 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-16 13:03:51.029  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 390.171 seconds (JVM running for 392.849)
2025-09-16 13:03:51.053  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-16 13:03:51.054  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-16 13:03:51.054  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-16 13:03:51.054  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:4471,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture14186100570563978322.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-16 13:03:51.055  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_3789132940=1
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-16 13:03:51.057  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-16 13:03:51.058  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1262719628=1
2025-09-16 13:03:51.059  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-16 13:03:51.060  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-16 13:03:51.060  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-16 13:03:51.060  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-16 13:03:51.060  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-16 13:03:51.060  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2283032206=1
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2775293581=1
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-16 13:03:51.061  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-16 13:03:51.062  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1592913036=1
2025-09-16 13:03:51.062  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-16 13:03:51.062  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-16 13:03:51.062  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-16 13:03:51.062  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-16 13:03:51.062  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-16 13:03:51.063  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-16 13:03:51.063  INFO 22344 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-16 13:03:51.063  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-16 13:03:51.063  INFO 22344 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-16 13:03:51.187  INFO 22344 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:03:51.188  INFO 22344 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:03:51.382 ERROR 22344 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:03:51.411  INFO 22344 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:03:51.439  INFO 22344 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-16 13:03:51.450  INFO 22344 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-16 13:03:59.372  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 25108 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-16 13:03:59.402  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-16 13:03:59.664  INFO 25108 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-16 13:03:59.664  INFO 25108 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-16 13:04:02.809  INFO 25108 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16 13:04:03.620  INFO 25108 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 783 ms. Found 15 JPA repository interfaces.
2025-09-16 13:04:05.873  INFO 25108 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-16 13:04:05.894  INFO 25108 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-16 13:04:05.896  INFO 25108 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-16 13:04:06.158  INFO 25108 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-16 13:04:06.158  INFO 25108 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6490 ms
2025-09-16 13:04:06.803  INFO 25108 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-16 13:04:07.156  INFO 25108 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-16 13:04:07.587  INFO 25108 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-16 13:04:07.854  INFO 25108 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-16 13:04:08.133  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-16 13:04:08.140  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-16 13:04:08.142  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-16 13:04:08.145  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-16 13:04:08.149  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-16 13:04:08.154  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-16 13:04:08.157  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-16 13:04:08.159  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-16 13:04:08.163  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-16 13:04:08.166  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-16 13:04:08.169  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-16 13:04:08.172  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-16 13:04:08.175  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-16 13:04:08.178  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-16 13:04:08.181  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-16 13:04:08.185  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-16 13:04:08.187  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-16 13:04:08.190  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-16 13:04:08.192  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-16 13:04:08.199  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-16 13:04:08.202  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-16 13:04:08.205  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-16 13:04:08.207  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-16 13:04:08.211  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-16 13:04:08.215  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-16 13:04:08.217  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-16 13:04:08.220  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-16 13:04:08.223  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-16 13:04:08.227  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-16 13:04:08.230  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-16 13:04:08.282  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-16 13:04:08.285  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-16 13:04:08.288  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-16 13:04:08.291  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-16 13:04:08.297  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-16 13:04:08.301  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-16 13:04:08.304  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-16 13:04:08.308  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-16 13:04:08.312  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-16 13:04:08.316  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-16 13:04:08.320  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-16 13:04:08.323  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-16 13:04:08.325  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-16 13:04:08.328  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-16 13:04:08.332  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-16 13:04:08.334  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-16 13:04:08.339  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-16 13:04:08.341  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-16 13:04:08.344  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-16 13:04:08.347  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-16 13:04:08.351  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-16 13:04:08.356  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-16 13:04:08.377  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-16 13:04:08.380  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-16 13:04:08.387  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-16 13:04:08.391  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-16 13:04:08.393  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-16 13:04:08.555  INFO 25108 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-16 13:04:08.768  INFO 25108 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-16 13:04:08.941  INFO 25108 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16 13:04:09.112  INFO 25108 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-16 13:04:09.493  INFO 25108 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-16 13:04:09.776  INFO 25108 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-16 13:04:10.440  INFO 25108 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 13:04:10.453  INFO 25108 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 13:04:12.448  INFO 25108 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-16 13:04:12.470  INFO 25108 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:04:13.475  INFO 25108 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 113 ms
2025-09-16 13:04:15.010  INFO 25108 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1110 ms
2025-09-16 13:04:15.259  INFO 25108 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-16 13:04:15.340  INFO 25108 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 56 ms.
2025-09-16 13:04:15.520  INFO 25108 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-16 13:04:15.521  INFO 25108 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-16 13:04:15.652  INFO 25108 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 130 ms.
2025-09-16 13:04:17.522  INFO 25108 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=6894, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=2025-09-15T23:17:55.899497, updatedAt=2025-09-16T11:27:22.742881, splReadiness=БГ № 2, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1124, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1032, createdAt=2025-09-16T11:11:12.028664, plantMissile='11', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=true, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=LaunchInitialDataDao{id=1030, createdAt=2025-09-16T11:11:12.676411, loadTemperature=-999.9, latitudeRad=45.12877000, longitudeRad=31.67633000, altitude=0.0, inclinationAngle=-75.0, trajectory=AERO_BALLISTIC, readiness=БГ № 2, isProDetected=true, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=true, startTime2025-09-16T11:41:12.570784'}OrderInfoDao{id=24, orderEntityId=aa29b624-0e2b-4687-a2e6-b268422cdb11, validUntil=2025-09-16T12:11:12.570784}, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='true', storedTlKeys={123;123---sdfsdfsfd1465ssdfsd====}
, sensorTemperature=15.6, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@f00f9744}
2025-09-16 13:04:52.462  WARN 25108 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=45s194ms63µs100ns).
2025-09-16 13:11:22.762  WARN 25108 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m30s299ms414µs400ns).
2025-09-16 13:11:23.944  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@9c4c9d54, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@87e2528b, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ebdda52, org.springframework.security.web.header.HeaderWriterFilter@a95223e8, org.springframework.security.web.authentication.logout.LogoutFilter@cc6c8a13, com.deb.spl.control.authorization.AuthenticationFilter@f5bb69bd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b1b0db49, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@f2c84dee, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@8fb4c588, org.springframework.security.web.session.SessionManagementFilter@bd217514, org.springframework.security.web.access.ExceptionTranslationFilter@50bcf398, org.springframework.security.web.access.intercept.AuthorizationFilter@8a555739]
2025-09-16 13:11:23.952  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.953  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-16 13:11:23.954  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.954  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-16 13:11:23.954  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.954  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-16 13:11:23.954  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.954  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-16 13:11:23.954  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.954  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-16 13:11:23.954  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.954  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-16 13:11:23.955  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.955  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-16 13:11:23.957  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.957  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-16 13:11:23.957  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.957  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-16 13:11:23.957  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.957  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-16 13:11:23.957  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.957  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-16 13:11:23.957  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.957  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-16 13:11:23.957  WARN 25108 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:11:23.957  INFO 25108 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-16 13:11:24.598  WARN 25108 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-16 13:11:25.891  INFO 25108 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-16 13:11:26.172  INFO 25108 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-16 13:11:26.264  INFO 25108 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-16 13:11:26.355  INFO 25108 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-16 13:11:29.287  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 452.534 seconds (JVM running for 454.945)
2025-09-16 13:11:29.322  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-16 13:11:29.323  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-16 13:11:29.324  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-16 13:11:29.324  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:11032,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture1303253729254108015.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-16 13:11:29.334  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_3789132940=1
2025-09-16 13:11:29.335  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-16 13:11:29.336  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-16 13:11:29.336  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-16 13:11:29.336  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-16 13:11:29.336  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-16 13:11:29.336  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-16 13:11:29.336  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-16 13:11:29.337  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1262719628=1
2025-09-16 13:11:29.338  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2283032206=1
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2775293581=1
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-16 13:11:29.339  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-16 13:11:29.340  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-16 13:11:29.340  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-16 13:11:29.340  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1592913036=1
2025-09-16 13:11:29.340  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-16 13:11:29.340  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-16 13:11:29.341  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-16 13:11:29.342  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-16 13:11:29.342  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-16 13:11:29.342  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-16 13:11:29.342  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-16 13:11:29.343  INFO 25108 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-16 13:11:29.450  INFO 25108 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:11:29.450  INFO 25108 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:11:39.532 ERROR 25108 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:11:39.559  INFO 25108 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:11:39.575  INFO 25108 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-16 13:11:39.590  INFO 25108 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-16 13:12:28.139  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 17704 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-16 13:12:28.145  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-16 13:12:28.402  INFO 17704 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-16 13:12:28.403  INFO 17704 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-16 13:12:30.991  INFO 17704 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16 13:12:31.612  INFO 17704 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 599 ms. Found 15 JPA repository interfaces.
2025-09-16 13:12:33.428  INFO 17704 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-16 13:12:33.454  INFO 17704 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-16 13:12:33.455  INFO 17704 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-16 13:12:33.689  INFO 17704 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-16 13:12:33.689  INFO 17704 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5282 ms
2025-09-16 13:12:34.452  INFO 17704 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-16 13:12:34.728  INFO 17704 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-16 13:12:35.048  INFO 17704 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-16 13:12:35.250  INFO 17704 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-16 13:12:35.554  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-16 13:12:35.558  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-16 13:12:35.561  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-16 13:12:35.564  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-16 13:12:35.568  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-16 13:12:35.571  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-16 13:12:35.573  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-16 13:12:35.576  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-16 13:12:35.579  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-16 13:12:35.582  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-16 13:12:35.585  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-16 13:12:35.589  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-16 13:12:35.592  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-16 13:12:35.595  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-16 13:12:35.598  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-16 13:12:35.601  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-16 13:12:35.604  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-16 13:12:35.608  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-16 13:12:35.610  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-16 13:12:35.614  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-16 13:12:35.617  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-16 13:12:35.619  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-16 13:12:35.622  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-16 13:12:35.626  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-16 13:12:35.629  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-16 13:12:35.631  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-16 13:12:35.635  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-16 13:12:35.638  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-16 13:12:35.641  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-16 13:12:35.644  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-16 13:12:35.698  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-16 13:12:35.701  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-16 13:12:35.703  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-16 13:12:35.708  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-16 13:12:35.711  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-16 13:12:35.714  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-16 13:12:35.716  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-16 13:12:35.719  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-16 13:12:35.721  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-16 13:12:35.723  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-16 13:12:35.726  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-16 13:12:35.728  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-16 13:12:35.732  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-16 13:12:35.734  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-16 13:12:35.736  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-16 13:12:35.739  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-16 13:12:35.742  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-16 13:12:35.747  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-16 13:12:35.750  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-16 13:12:35.753  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-16 13:12:35.755  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-16 13:12:35.757  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-16 13:12:35.777  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-16 13:12:35.780  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-16 13:12:35.784  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-16 13:12:35.787  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-16 13:12:35.790  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-16 13:12:35.961  INFO 17704 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-16 13:12:36.158  INFO 17704 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-16 13:12:36.297  INFO 17704 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16 13:12:36.402  INFO 17704 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-16 13:12:36.765  INFO 17704 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-16 13:12:37.045  INFO 17704 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-16 13:12:37.627  INFO 17704 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 13:12:37.642  INFO 17704 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 13:12:40.095  INFO 17704 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-16 13:12:40.124  INFO 17704 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:12:41.396  INFO 17704 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 136 ms
2025-09-16 13:12:43.355  INFO 17704 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1456 ms
2025-09-16 13:12:43.638  INFO 17704 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-16 13:12:43.745  INFO 17704 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 73 ms.
2025-09-16 13:12:43.943  INFO 17704 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-16 13:12:43.944  INFO 17704 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-16 13:12:44.047  INFO 17704 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 102 ms.
2025-09-16 13:12:46.383  INFO 17704 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=6894, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=2025-09-15T23:17:55.899497, updatedAt=2025-09-16T11:27:22.742881, splReadiness=БГ № 2, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1124, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1032, createdAt=2025-09-16T11:11:12.028664, plantMissile='11', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=true, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=LaunchInitialDataDao{id=1030, createdAt=2025-09-16T11:11:12.676411, loadTemperature=-999.9, latitudeRad=45.12877000, longitudeRad=31.67633000, altitude=0.0, inclinationAngle=-75.0, trajectory=AERO_BALLISTIC, readiness=БГ № 2, isProDetected=true, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=true, startTime2025-09-16T11:41:12.570784'}OrderInfoDao{id=24, orderEntityId=aa29b624-0e2b-4687-a2e6-b268422cdb11, validUntil=2025-09-16T12:11:12.570784}, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='true', storedTlKeys={123;123---sdfsdfsfd1465ssdfsd====}
, sensorTemperature=15.6, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@58e6a532}
2025-09-16 13:13:20.163  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@e6e20e48, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@f17b346f, org.springframework.security.web.context.SecurityContextPersistenceFilter@f0bf3b0d, org.springframework.security.web.header.HeaderWriterFilter@73fdb783, org.springframework.security.web.authentication.logout.LogoutFilter@79938b0f, com.deb.spl.control.authorization.AuthenticationFilter@cf4e7f8f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a2485d7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40e5c5e5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@649a303b, org.springframework.security.web.session.SessionManagementFilter@aa2840e8, org.springframework.security.web.access.ExceptionTranslationFilter@fd1246cb, org.springframework.security.web.access.intercept.AuthorizationFilter@6210d517]
2025-09-16 13:13:20.174  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.175  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-16 13:13:20.177  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.178  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-16 13:13:20.178  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.178  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-16 13:13:20.178  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.179  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-16 13:13:20.179  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.179  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-16 13:13:20.179  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.179  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-16 13:13:20.179  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.179  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-16 13:13:20.179  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.179  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-16 13:13:20.179  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.179  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-16 13:13:20.180  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.180  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-16 13:13:20.180  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.180  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-16 13:13:20.180  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.180  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-16 13:13:20.180  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.180  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-16 13:13:20.180  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.180  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-16 13:13:20.181  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.181  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-16 13:13:20.181  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.181  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-16 13:13:20.181  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.181  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-16 13:13:20.181  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.181  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-16 13:13:20.181  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.181  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-16 13:13:20.181  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.182  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-16 13:13:20.182  WARN 17704 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:13:20.182  INFO 17704 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-16 13:13:20.637  WARN 17704 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-16 13:13:21.973  INFO 17704 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-16 13:13:22.501  INFO 17704 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-16 13:13:22.652  INFO 17704 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-16 13:13:22.691  INFO 17704 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-16 13:13:24.792  INFO 17704 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 13:13:24.792  INFO 17704 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-16 13:13:24.797  INFO 17704 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-09-16 13:13:24.821  INFO 17704 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/suto/; 	 requestQueryString null; 	 payload {
  "id": 14084,
  "status": "OK",
  "stats": {
    "roll": 0,
    "pitch": 0,
    "armLiftStrokePosition": 0,
    "levelingCyclesCount": 0,
    "pressureInImpulseSection": 0.0,
    "temperatureRR": 0,
    "workingFluidLevel": 0,
    "mainPumpRPM": 0,
    "overallOperatingTime": 0,
    "overallArmLiftingsCount": 0
  },
  "leftFrontOutriggerEmergencyCode": 0,
  "rightFrontOutriggerEmergencyCode": 0,
  "leftRearOutriggerEmergencyCode": 0,
  "rightRearOutriggerEmergencyCode": 0,
  "properties": [
    {
      "id": 1,
      "name": "chassisHorizontalAlignment",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 2,
      "name": "isChassisHorizontal",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 3,
      "name": "isAlignmentImpossible",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 4,
      "name": "isOutriggersMovingToPP",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 5,
      "name": "isOutriggersInMobileState",
      "state": "ON",
      "propertyType": "REGULAR"
    },
    {
      "id": 6,
      "name": "isEmergencyHangingAndAlignment",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 7,
      "name": "leftFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 8,
      "name": "rightFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 9,
      "name": "rightRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 10,
      "name": "leftRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 11,
      "name": "armUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 12,
      "name": "armRaising",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 13,
      "name": "armRaised",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 14,
      "name": "leftGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 15,
      "name": "rightGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 16,
      "name": "isLoweringLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 17,
      "name": "isLoweringRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 18,
      "name": "emergencySituationForArmAndGasSprings",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 19,
      "name": "leftGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 20,
      "name": "rightGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 21,
      "name": "isRaisingLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 22,
      "name": "isRaisingRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 23,
      "name": "leftGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 24,
      "name": "rightGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 25,
      "name": "leftGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 26,
      "name": "rightGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 27,
      "name": "isLoweringArm",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 28,
      "name": "armInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 29,
      "name": "boomLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 30,
      "name": "boomInEndPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 31,
      "name": "boomEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 32,
      "name": "boomLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 33,
      "name": "leftGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 34,
      "name": "rightGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 35,
      "name": "malfunctionLeftGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 36,
      "name": "malfunctionRightGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 37,
      "name": "armEmptyStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 38,
      "name": "armPistonStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 39,
      "name": "SPLEngineStarting",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 40,
      "name": "SPLEngineStarted",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 41,
      "name": "mainPumpConnectionToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 42,
      "name": "mainPumpConnectedToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 43,
      "name": "unloadingElectromagnetEnabled",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 44,
      "name": "SPLEnginestopping",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 45,
      "name": "emergencySituation",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 46,
      "name": "engineStartImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 47,
      "name": "engineStartImpossible2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 48,
      "name": "engineStopImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 49,
      "name": "fire",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 50,
      "name": "pollutedFilterDZF1",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 51,
      "name": "pollutedFilterDZF2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 52,
      "name": "airPressureNotNormal",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 53,
      "name": "leftFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 54,
      "name": "leftFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 55,
      "name": "rightFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 56,
      "name": "rightFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 57,
      "name": "rightRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 58,
      "name": "rightRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 59,
      "name": "leftRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 60,
      "name": "leftRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 61,
      "name": "leftFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 62,
      "name": "leftFrontLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 63,
      "name": "rightFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 64,
      "name": "rightFrontLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 65,
      "name": "rightRearLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 66,
      "name": "rightRearLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 67,
      "name": "leftRearLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 68,
      "name": "leftRearLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 69,
      "name": "SUTOStop",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    }
  ]
}
2025-09-16 13:13:24.945  INFO 17704 --- [http-nio-8079-exec-1] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=63563, createdAt=2025-09-16T13:13:24.859694200, direction=IN, adjacentSystem=SUTO, method='null', url='http://localhost:8079/api/v1/adjacent-systems/suto/', cachedPayload='{
  "id": 14084,
  "status": "OK",
  "stats": {
    "roll": 0,
    "pitch": 0,
    "armLiftStrokePosition": 0,
    "levelingCyclesCount": 0,
    "pressureInImpulseSection": 0.0,
    "temperatureRR": 0,
    "workingFluidLevel": 0,
    "mainPumpRPM": 0,
    "overallOperatingTime": 0,
    "overallArmLiftingsCount": 0
  },
  "leftFrontOutriggerEmergencyCode": 0,
  "rightFrontOutriggerEmergencyCode": 0,
  "leftRearOutriggerEmergencyCode": 0,
  "rightRearOutriggerEmergencyCode": 0,
  "properties": [
    {
      "id": 1,
      "name": "chassisHorizontalAlignment",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 2,
      "name": "isChassisHorizontal",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 3,
      "name": "isAlignmentImpossible",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 4,
      "name": "isOutriggersMovingToPP",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 5,
      "name": "isOutriggersInMobileState",
      "state": "ON",
      "propertyType": "REGULAR"
    },
    {
      "id": 6,
      "name": "isEmergencyHangingAndAlignment",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 7,
      "name": "leftFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 8,
      "name": "rightFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 9,
      "name": "rightRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 10,
      "name": "leftRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 11,
      "name": "armUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 12,
      "name": "armRaising",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 13,
      "name": "armRaised",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 14,
      "name": "leftGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 15,
      "name": "rightGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 16,
      "name": "isLoweringLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 17,
      "name": "isLoweringRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 18,
      "name": "emergencySituationForArmAndGasSprings",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 19,
      "name": "leftGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 20,
      "name": "rightGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 21,
      "name": "isRaisingLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 22,
      "name": "isRaisingRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 23,
      "name": "leftGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 24,
      "name": "rightGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 25,
      "name": "leftGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 26,
      "name": "rightGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 27,
      "name": "isLoweringArm",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 28,
      "name": "armInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 29,
      "name": "boomLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 30,
      "name": "boomInEndPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 31,
      "name": "boomEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 32,
      "name": "boomLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 33,
      "name": "leftGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 34,
      "name": "rightGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 35,
      "name": "malfunctionLeftGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 36,
      "name": "malfunctionRightGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 37,
      "name": "armEmptyStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 38,
      "name": "armPistonStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 39,
      "name": "SPLEngineStarting",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 40,
      "name": "SPLEngineStarted",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 41,
      "name": "mainPumpConnectionToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 42,
      "name": "mainPumpConnectedToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 43,
      "name": "unloadingElectromagnetEnabled",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 44,
      "name": "SPLEnginestopping",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 45,
      "name": "emergencySituation",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 46,
      "name": "engineStartImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 47,
      "name": "engineStartImpossible2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 48,
      "name": "engineStopImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 49,
      "name": "fire",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 50,
      "name": "pollutedFilterDZF1",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 51,
      "name": "pollutedFilterDZF2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 52,
      "name": "airPressureNotNormal",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 53,
      "name": "leftFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 54,
      "name": "leftFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 55,
      "name": "rightFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 56,
      "name": "rightFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 57,
      "name": "rightRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 58,
      "name": "rightRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 59,
      "name": "leftRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 60,
      "name": "leftRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 61,
      "name": "leftFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "R', requestQueryString='null', headers=null}
2025-09-16 13:15:38.016  WARN 17704 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m49s427ms409µs900ns).
2025-09-16 13:17:13.381  WARN 17704 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m35s366ms268µs700ns).
2025-09-16 13:17:13.417  INFO 17704 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-16 13:17:13.457  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 287.435 seconds (JVM running for 289.937)
2025-09-16 13:17:13.538  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-16 13:17:13.568  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-16 13:17:13.568  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-16 13:17:13.574  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:3397,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture6618406925706962278.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-16 13:17:13.587  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-16 13:17:13.588  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_3789132940=1
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-16 13:17:13.590  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-16 13:17:13.591  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-16 13:17:13.595  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-16 13:17:13.596  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-16 13:17:13.596  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-16 13:17:13.596  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-16 13:17:13.597  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-16 13:17:13.598  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-16 13:17:13.600  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-16 13:17:13.601  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-16 13:17:13.603  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-16 13:17:13.604  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-16 13:17:13.606  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-16 13:17:13.614  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-16 13:17:13.614  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-16 13:17:13.615  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-16 13:17:13.615  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-16 13:17:13.616  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-16 13:17:13.621  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-16 13:17:13.621  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-16 13:17:13.622  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1262719628=1
2025-09-16 13:17:13.631  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-16 13:17:13.635  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-16 13:17:13.638  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-16 13:17:13.639  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-16 13:17:13.640  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-16 13:17:13.640  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-16 13:17:13.640  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-16 13:17:13.640  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2283032206=1
2025-09-16 13:17:13.645  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2775293581=1
2025-09-16 13:17:13.650  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-16 13:17:13.653  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1592913036=1
2025-09-16 13:17:13.654  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-16 13:17:13.658  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-16 13:17:13.658  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-16 13:17:13.658  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-16 13:17:13.659  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-16 13:17:13.659  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-16 13:17:13.659  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-16 13:17:13.659  INFO 17704 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-16 13:17:13.849  INFO 17704 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:17:13.862  INFO 17704 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:17:17.729 ERROR 17704 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-16 13:17:17.749 ERROR 17704 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-16 13:17:18.150 ERROR 17704 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-16 13:17:18.151 ERROR 17704 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-16T13:17:18.151165700
2025-09-16 13:17:18.152  INFO 17704 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:17:18.244 ERROR 17704 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:17:19.138  INFO 17704 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:17:19.200 ERROR 17704 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-16 13:17:21.013 ERROR 17704 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-16T13:17:21.012576500
2025-09-16 13:17:21.297 ERROR 17704 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:17:21.338  INFO 17704 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:17:21.353  INFO 17704 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-16 13:17:21.366  INFO 17704 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-16 13:17:21.368 ERROR 17704 --- [scheduling-1] o.s.s.s.TaskUtils$LoggingErrorHandler    : Unexpected error occurred in scheduled task

org.springframework.transaction.CannotCreateTransactionException: Could not open JPA EntityManager for transaction; nested exception is java.lang.IllegalStateException: EntityManagerFactory is closed
	at org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:467) ~[spring-orm-5.3.25.jar:5.3.25]
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400) ~[spring-tx-5.3.25.jar:5.3.25]
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373) ~[spring-tx-5.3.25.jar:5.3.25]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595) ~[spring-tx-5.3.25.jar:5.3.25]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382) ~[spring-tx-5.3.25.jar:5.3.25]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.25.jar:5.3.25]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.25.jar:5.3.25]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.25.jar:5.3.25]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.25.jar:5.3.25]
	at com.deb.spl.control.service.NppaService$$EnhancerBySpringCGLIB$$b81c6ea.updateByn(<generated>) ~[classes/:na]
	at com.deb.spl.control.service.asku.PlcService.resetConnectedSystemStatues(PlcService.java:149) ~[classes/:na]
	at com.deb.spl.control.service.asku.PlcService.fireUpdateEvent(PlcService.java:112) ~[classes/:na]
	at com.deb.spl.control.service.asku.AskuTasksExecutorService.updatePlc(AskuTasksExecutorService.java:33) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) ~[spring-context-5.3.25.jar:5.3.25]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-5.3.25.jar:5.3.25]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:858) ~[na:na]
Caused by: java.lang.IllegalStateException: EntityManagerFactory is closed
	at org.hibernate.internal.SessionFactoryImpl.validateNotClosed(SessionFactoryImpl.java:547) ~[hibernate-core-5.6.14.Final.jar:5.6.14.Final]
	at org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:636) ~[hibernate-core-5.6.14.Final.jar:5.6.14.Final]
	at org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:158) ~[hibernate-core-5.6.14.Final.jar:5.6.14.Final]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.createNativeEntityManager(AbstractEntityManagerFactoryBean.java:585) ~[spring-orm-5.3.25.jar:5.3.25]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:487) ~[spring-orm-5.3.25.jar:5.3.25]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:734) ~[spring-orm-5.3.25.jar:5.3.25]
	at jdk.proxy3/jdk.proxy3.$Proxy160.createNativeEntityManager(Unknown Source) ~[na:na]
	at org.springframework.orm.jpa.JpaTransactionManager.createEntityManagerForTransaction(JpaTransactionManager.java:485) ~[spring-orm-5.3.25.jar:5.3.25]
	at org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:410) ~[spring-orm-5.3.25.jar:5.3.25]
	... 25 common frames omitted

2025-09-16 13:33:20.913  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : Starting Application using Java ******** on WIN-ARR4C6ROFQD with PID 23676 (D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes started by admin in D:\GitHub\flow-crm-tutorial\vaadin-header)
2025-09-16 13:33:20.931  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : The following 1 profile is active: "dev"
2025-09-16 13:33:21.232  INFO 23676 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-09-16 13:33:21.233  INFO 23676 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-09-16 13:33:24.222  INFO 23676 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-16 13:33:24.907  INFO 23676 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 662 ms. Found 15 JPA repository interfaces.
2025-09-16 13:33:27.418  INFO 23676 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8079 (http)
2025-09-16 13:33:27.438  INFO 23676 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-16 13:33:27.438  INFO 23676 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-09-16 13:33:27.671  INFO 23676 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-16 13:33:27.671  INFO 23676 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6435 ms
2025-09-16 13:33:28.239  INFO 23676 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-16 13:33:28.551  INFO 23676 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-16 13:33:28.903  INFO 23676 --- [restartedMain] liquibase.database                       : Set default schema name to public
2025-09-16 13:33:29.139  INFO 23676 --- [restartedMain] liquibase.lockservice                    : Successfully acquired change log lock
2025-09-16 13:33:29.458  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/07/changeset_03.sql
2025-09-16 13:33:29.462  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_04.sql
2025-09-16 13:33:29.465  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_05.sql
2025-09-16 13:33:29.469  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_06.sql
2025-09-16 13:33:29.474  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_07.sql
2025-09-16 13:33:29.477  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_08.sql
2025-09-16 13:33:29.481  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_09.sql
2025-09-16 13:33:29.487  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_10.sql
2025-09-16 13:33:29.490  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_11.sql
2025-09-16 13:33:29.493  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_12.sql
2025-09-16 13:33:29.497  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_13.sql
2025-09-16 13:33:29.500  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_14.sql
2025-09-16 13:33:29.505  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_15.sql
2025-09-16 13:33:29.508  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/08/changeset_16.sql
2025-09-16 13:33:29.511  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_17.sql
2025-09-16 13:33:29.514  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_18.sql
2025-09-16 13:33:29.519  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_19.sql
2025-09-16 13:33:29.522  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_20.sql
2025-09-16 13:33:29.524  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_21.sql
2025-09-16 13:33:29.528  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_22.sql
2025-09-16 13:33:29.530  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/09/changeset_23.sql
2025-09-16 13:33:29.534  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/10/changeset_24.sql
2025-09-16 13:33:29.540  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_26.sql
2025-09-16 13:33:29.543  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_27.sql
2025-09-16 13:33:29.547  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_28.sql
2025-09-16 13:33:29.550  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/11/changeset_29.sql
2025-09-16 13:33:29.554  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_30.sql
2025-09-16 13:33:29.557  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/12/changeset_31.sql
2025-09-16 13:33:29.561  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_33.sql
2025-09-16 13:33:29.565  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2023/13/changeset_34.sql
2025-09-16 13:33:29.639  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_35.sql
2025-09-16 13:33:29.641  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_36.sql
2025-09-16 13:33:29.645  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_37.sql
2025-09-16 13:33:29.650  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changeset_38.sql
2025-09-16 13:33:29.654  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_39.sql
2025-09-16 13:33:29.656  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/01/changset_40.sql
2025-09-16 13:33:29.659  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_41.sql
2025-09-16 13:33:29.661  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_42.sql
2025-09-16 13:33:29.665  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_43.sql
2025-09-16 13:33:29.671  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_44.sql
2025-09-16 13:33:29.675  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_45.sql
2025-09-16 13:33:29.677  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_46.sql
2025-09-16 13:33:29.681  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/04/changset_47.sql
2025-09-16 13:33:29.689  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/05/changset_48.sql
2025-09-16 13:33:29.692  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_49.sql
2025-09-16 13:33:29.694  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_50.sql
2025-09-16 13:33:29.698  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_51.sql
2025-09-16 13:33:29.701  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_52.sql
2025-09-16 13:33:29.707  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/06/changset_53.sql
2025-09-16 13:33:29.709  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/07/changset_54.sql
2025-09-16 13:33:29.711  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_54.sql
2025-09-16 13:33:29.716  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2024/09/changset_55.sql
2025-09-16 13:33:29.731  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_56.sql
2025-09-16 13:33:29.733  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_57.sql
2025-09-16 13:33:29.739  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/01/changset_58.sql
2025-09-16 13:33:29.740  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/02/changset_59.sql
2025-09-16 13:33:29.744  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading resource: db/changelog/2025/08/changset_60.sql
2025-09-16 13:33:29.979  INFO 23676 --- [restartedMain] liquibase.changelog                      : Reading from public.databasechangelog
2025-09-16 13:33:30.347  INFO 23676 --- [restartedMain] liquibase.lockservice                    : Successfully released change log lock
2025-09-16 13:33:30.583  INFO 23676 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-16 13:33:30.696  INFO 23676 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.14.Final
2025-09-16 13:33:31.075  INFO 23676 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-16 13:33:31.389  INFO 23676 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL10Dialect
2025-09-16 13:33:32.061  INFO 23676 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 13:33:32.070  INFO 23676 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.HSQLDialect
2025-09-16 13:33:34.311  INFO 23676 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-16 13:33:34.337  INFO 23676 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:33:36.346  INFO 23676 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for VaadinAppShell took 185 ms
2025-09-16 13:33:38.461  INFO 23676 --- [restartedMain] c.v.f.s.VaadinServletContextInitializer  : Search for subclasses and classes with annotations took 1406 ms
2025-09-16 13:33:38.774  INFO 23676 --- [restartedMain] c.v.b.d.startup.DevModeStartupListener   : Starting dev-mode updaters in D:\GitHub\flow-crm-tutorial\vaadin-header folder.
2025-09-16 13:33:38.867  INFO 23676 --- [restartedMain] c.v.f.s.f.s.FullDependenciesScanner      : Visited 153 classes. Took 63 ms.
2025-09-16 13:33:39.048  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskUpdatePackages      : Skipping `npm install` because the frontend packages are already installed in the folder 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules' and the hash in the file 'D:\GitHub\flow-crm-tutorial\vaadin-header\node_modules\.vaadin\vaadin.json' is the same as in 'package.json'
2025-09-16 13:33:39.049  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Copying frontend resources from jar files ...
2025-09-16 13:33:39.156  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.v.f.s.frontend.TaskCopyFrontendFiles   : Visited 25 resources. Took 107 ms.
2025-09-16 13:33:42.379  INFO 23676 --- [restartedMain] c.d.s.control.service.asku.AskuService   : ASKU loaded from DB AskuDao{id=6894, splId=02d9dd7e-8cda-40f1-b068-b04a23841097, plateNumber='DP 0101 UA', unitTitle='spl101', startedDate=2025-09-15T23:17:55.899497, updatedAt=2025-09-16T11:27:22.742881, splReadiness=БГ № 2, tpcLeft=Tpc{id=1, dateReleaseM=2020-07-21T12:40:45, tpcLoadState=TPC_WITH_ROCKET}, tpcRight=Tpc{id=255, dateReleaseM=2023-08-29T18:33:51.542816, tpcLoadState=TPC_WITH_ROCKET}, leftRocket=RocketDao{id=1124, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=1032, createdAt=2025-09-16T11:11:12.028664, plantMissile='11', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=true, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=LaunchInitialDataDao{id=1030, createdAt=2025-09-16T11:11:12.676411, loadTemperature=-999.9, latitudeRad=45.12877000, longitudeRad=31.67633000, altitude=0.0, inclinationAngle=-75.0, trajectory=AERO_BALLISTIC, readiness=БГ № 2, isProDetected=true, missileOperatingMode=BASE_SNS, tlCode='gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ==, scheduled=true, startTime2025-09-16T11:41:12.570784'}OrderInfoDao{id=24, orderEntityId=aa29b624-0e2b-4687-a2e6-b268422cdb11, validUntil=2025-09-16T12:11:12.570784}, initialDataSource=MSG, initialDataSourceDescription='не вказан, loadedToPlc='true', storedTlKeys={123;123---sdfsdfsfd1465ssdfsd====}
, sensorTemperature=15.6, launchResultCode , launchResultDescription}, rightRocket=RocketDao{id=204, dateReleaseM=null, technicalCondition=true, dateUseM=null, formData=RocketFormDataDao{id=235, createdAt=2023-08-29T18:33:51.501015, plantMissile='84', warhead=CBCH, gsnType=NO_GSN, alpType=NO_ALP, isTelemetryIntegrated=false, purposeType=COMBAT, iArchived=false, crc='null'}, initialData=null, initialDataSource=MSG, initialDataSourceDescription='asd, loadedToPlc='false', storedTlKeys={qweqweqw23131aassssssssssssss///=====}
, sensorTemperature=-999.9, launchResultCode , launchResultDescription}, status=null, plc=com.deb.spl.control.data.asku.Plc@88399b87}
2025-09-16 13:35:03.810  WARN 23676 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m35s146ms522µs100ns).
2025-09-16 13:35:37.118  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3fb1802b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5612fa9b, org.springframework.security.web.context.SecurityContextPersistenceFilter@9fadca0c, org.springframework.security.web.header.HeaderWriterFilter@be1e7a48, org.springframework.security.web.authentication.logout.LogoutFilter@223ccd06, com.deb.spl.control.authorization.AuthenticationFilter@93540d60, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@db1a9ebe, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1fc863f0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@650bde94, org.springframework.security.web.session.SessionManagementFilter@86d5531, org.springframework.security.web.access.ExceptionTranslationFilter@f1ce8fc2, org.springframework.security.web.access.intercept.AuthorizationFilter@5156f114]
2025-09-16 13:35:37.126  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/images/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.126  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/images/**']
2025-09-16 13:35:37.128  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/VAADIN/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.129  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/VAADIN/**']
2025-09-16 13:35:37.129  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.129  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/']
2025-09-16 13:35:37.129  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/adjacent-systems/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.129  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/adjacent-systems/**']
2025-09-16 13:35:37.129  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/automation/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.129  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/automation/**']
2025-09-16 13:35:37.129  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/readiness/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.129  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/readiness/**']
2025-09-16 13:35:37.130  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/log_main_view/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.130  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/log_main_view/**']
2025-09-16 13:35:37.130  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/mmhs']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.130  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/mmhs']
2025-09-16 13:35:37.130  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/adjacent-systems/bins/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.130  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/adjacent-systems/bins/**']
2025-09-16 13:35:37.130  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.130  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/tlc/open_keys/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.131  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/tlc/open_keys/**']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/initial_data/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.131  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/initial_data/**']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/fire_order/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.131  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/fire_order/**']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.131  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/state/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.131  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/state/']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.131  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points/']
2025-09-16 13:35:37.131  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/api/v1/asku/reference_points']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.134  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/api/v1/asku/reference_points']
2025-09-16 13:35:37.135  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.136  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-api']
2025-09-16 13:35:37.136  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/create-admin-api']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.136  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/create-admin-api']
2025-09-16 13:35:37.136  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Ant [pattern='/users/test']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.137  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Ant [pattern='/users/test']
2025-09-16 13:35:37.137  WARN 23676 --- [restartedMain] o.s.s.c.a.web.builders.WebSecurity       : You are asking Spring Security to ignore Regex [pattern='^$']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-09-16 13:35:37.137  INFO 23676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will not secure Regex [pattern='^$']
2025-09-16 13:35:37.651  WARN 23676 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-16 13:35:39.478  INFO 23676 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-09-16 13:35:39.881  INFO 23676 --- [restartedMain] c.v.f.s.DefaultDeploymentConfiguration   : 
Vaadin is running in DEVELOPMENT mode - do not use for production deployments.

The following EXPERIMENTAL features are enabled:
- Enforce client / constraint / binder validation


2025-09-16 13:35:40.004  INFO 23676 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8079 (http) with context path ''
2025-09-16 13:35:40.026  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Starting Vite
2025-09-16 13:35:44.405  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Running Vite to compile frontend resources. This may take a moment, please stand by...
2025-09-16 13:35:45.173  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : Started Application in 149.418 seconds (JVM running for 152.418)
2025-09-16 13:35:45.242  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : temporary files location : D:\GitHub\flow-crm-tutorial\vaadin-header\tmp_files
2025-09-16 13:35:45.244  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN_ROAMINGPROFILE=WIN-ARR4C6ROFQD
2025-09-16 13:35:45.246  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : NVM_SYMLINK=C:\Program Files\nodejs
2025-09-16 13:35:45.246  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OPENJ9_JAVA_COMMAND_LINE=C:\Users\<USER>\.jdks\semeru-********\bin\java.exe -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:7476,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture978936908384851413.props -Dspring.profiles.active=dev -XX:TieredStopAtLevel=1 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -classpath "D:\GitHub\flow-crm-tutorial\vaadin-header\target\classes;C:\Users\<USER>\.m2\repository\net\sf\marineapi\marineapi\0.14.5\marineapi-0.14.5.jar;C:\Users\<USER>\.m2\repository\com\github\purejavacomm\purejavacomm\1.0.2.RELEASE\purejavacomm-1.0.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna-platform\4.2.2\jna-platform-4.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\librepdf\openpdf\1.3.33\openpdf-1.3.33.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\vaadin\olli\file-download-wrapper\7.0.0\file-download-wrapper-7.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-core\23.3.5\vaadin-core-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-server\23.3.3\flow-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\servletdetector\throw-if-servlet5\1.0.2\throw-if-servlet5-1.0.2.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;C:\Users\<USER>\.m2\repository\com\helger\ph-css\6.5.0\ph-css-6.5.0.jar;C:\Users\<USER>\.m2\repository\com\helger\commons\ph-commons\10.1.6\ph-commons-10.1.6.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gentyref\1.2.0.vaadin1\gentyref-1.2.0.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dev-server\23.3.3\vaadin-dev-server-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\open\8.5.0\open-8.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-lit-template\23.3.3\flow-lit-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-polymer-template\23.3.3\flow-polymer-template-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-push\23.3.3\flow-push-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\atmosphere\atmosphere-runtime\2.7.3.slf4jvaadin4\atmosphere-runtime-2.7.3.slf4jvaadin4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-client\23.3.3\flow-client-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-html-components\23.3.3\flow-html-components-23.3.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-data\23.3.3\flow-data-23.3.3.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\flow-dnd\23.3.3\flow-dnd-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\vaadin__vaadin-mobile-drag-drop\1.0.1\vaadin__vaadin-mobile-drag-drop-1.0.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\npm\mobile-drag-drop\2.3.0-rc.2\mobile-drag-drop-2.3.0-rc.2.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-lumo-theme\23.3.5\vaadin-lumo-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-material-theme\23.3.5\vaadin-material-theme-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-accordion-flow\23.3.5\vaadin-accordion-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-avatar-flow\23.3.5\vaadin-avatar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-flow-components-base\23.3.5\vaadin-flow-components-base-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-button-flow\23.3.5\vaadin-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-checkbox-flow\23.3.5\vaadin-checkbox-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-combo-box-flow\23.3.5\vaadin-combo-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-confirm-dialog-flow\23.3.5\vaadin-confirm-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-custom-field-flow\23.3.5\vaadin-custom-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-picker-flow\23.3.5\vaadin-date-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-date-time-picker-flow\23.3.5\vaadin-date-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-details-flow\23.3.5\vaadin-details-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-time-picker-flow\23.3.5\vaadin-time-picker-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-select-flow\23.3.5\vaadin-select-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-dialog-flow\23.3.5\vaadin-dialog-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-form-layout-flow\23.3.5\vaadin-form-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-field-highlighter-flow\23.3.5\vaadin-field-highlighter-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-flow\23.3.5\vaadin-grid-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-icons-flow\23.3.5\vaadin-icons-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-iron-list-flow\23.3.5\vaadin-iron-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-virtual-list-flow\23.3.5\vaadin-virtual-list-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-list-box-flow\23.3.5\vaadin-list-box-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-login-flow\23.3.5\vaadin-login-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-messages-flow\23.3.5\vaadin-messages-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-ordered-layout-flow\23.3.5\vaadin-ordered-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-progress-bar-flow\23.3.5\vaadin-progress-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-radio-button-flow\23.3.5\vaadin-radio-button-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-renderer-flow\23.3.5\vaadin-renderer-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-split-layout-flow\23.3.5\vaadin-split-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-tabs-flow\23.3.5\vaadin-tabs-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-text-field-flow\23.3.5\vaadin-text-field-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-upload-flow\23.3.5\vaadin-upload-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-notification-flow\23.3.5\vaadin-notification-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-app-layout-flow\23.3.5\vaadin-app-layout-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-context-menu-flow\23.3.5\vaadin-context-menu-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-menu-bar-flow\23.3.5\vaadin-menu-bar-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin\23.3.5\vaadin-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-board-flow\23.3.5\vaadin-board-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-charts-flow\23.3.5\vaadin-charts-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-cookie-consent-flow\23.3.5\vaadin-cookie-consent-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-crud-flow\23.3.5\vaadin-crud-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-grid-pro-flow\23.3.5\vaadin-grid-pro-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-map-flow\23.3.5\vaadin-map-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-rich-text-editor-flow\23.3.5\vaadin-rich-text-editor-flow-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\collaboration-engine\5.3.0\collaboration-engine-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\vaadin\license-checker\1.11.2\license-checker-1.11.2.jar;C:\Users\<USER>\.m2\repository\com\github\oshi\oshi-core\6.1.6\oshi-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\nimbusds\nimbus-jose-jwt\9.23\nimbus-jose-jwt-9.23.jar;C:\Users\<USER>\.m2\repository\org\lucee\jcip-annotations\1.0.0\jcip-annotations-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring-boot-starter\23.3.5\vaadin-spring-boot-starter-23.3.5.jar;C:\Users\<USER>\.m2\repository\com\vaadin\vaadin-spring\23.3.3\vaadin-spring-23.3.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.25\spring-webmvc-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.25\spring-websocket-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.8\spring-boot-starter-web-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.8\spring-boot-starter-tomcat-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.71\tomcat-embed-core-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.71\tomcat-embed-websocket-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\liquibase\liquibase-core\4.9.1\liquibase-core-4.9.1.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.7.8\spring-boot-starter-data-jpa-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.8\spring-boot-starter-aop-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.8\spring-boot-starter-jdbc-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.25\spring-jdbc-5.3.25.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.6.14.Final\hibernate-core-5.6.14.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.4.2.Final\jandex-2.4.2.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.7\jaxb-runtime-2.3.7.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.7\txw2-2.3.7.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.7.7\spring-data-jpa-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.7\spring-data-commons-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.3.25\spring-orm-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.25\spring-context-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.25\spring-tx-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.25\spring-beans-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.25\spring-aspects-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.8\spring-boot-starter-validation-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.8\spring-boot-starter-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.8\spring-boot-starter-logging-2.7.8.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.71\tomcat-embed-el-9.0.71.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.8\spring-boot-devtools-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.8\spring-boot-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.8\spring-boot-autoconfigure-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.8\spring-boot-starter-webflux-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.8\spring-boot-starter-json-2.7.8.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.8\spring-boot-starter-reactor-netty-2.7.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.27\reactor-netty-http-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.87.Final\netty-codec-http-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.87.Final\netty-codec-http2-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.87.Final\netty-resolver-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.87.Final\netty-codec-dns-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.87.Final\netty-resolver-dns-native-macos-4.1.87.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.87.Final\netty-resolver-dns-classes-macos-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.87.Final\netty-transport-native-epoll-4.1.87.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.27\reactor-netty-core-1.0.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.87.Final\netty-handler-proxy-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.25\spring-web-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.25\spring-webflux-5.3.25.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.26\reactor-core-3.4.26.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.7.8\spring-boot-starter-security-2.7.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.25\spring-aop-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.7.6\spring-security-config-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.7.6\spring-security-core-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.6\spring-security-crypto-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.7.6\spring-security-web-5.7.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.25\spring-expression-5.3.25.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.87.Final\netty-transport-classes-epoll-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.87.Final\netty-codec-socks-4.1.87.Final.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\gwt\gwt-elemental\2.8.2.vaadin2\gwt-elemental-2.8.2.vaadin2.jar;C:\Users\<USER>\.m2\repository\org\vaadin\tabs\paged-tabs\3.0.0\paged-tabs-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\componentfactory\togglebutton\1.0.2\togglebutton-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.25\spring-core-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.25\spring-jcl-5.3.25.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.8\spring-boot-test-2.7.8.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.11.0\jna-5.11.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.4.Final\mapstruct-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.5.4.Final\mapstruct-processor-1.5.4.Final.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\22.0.0\annotations-22.0.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\4.18.2\swagger-ui-4.18.2.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-data-rest\1.7.0\springdoc-openapi-data-rest-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-hateoas\1.7.0\springdoc-openapi-hateoas-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\hateoas\spring-hateoas\1.5.2\spring-hateoas-1.5.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-rest-core\3.7.7\spring-data-rest-core-3.7.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\atteo\evo-inflector\1.3\evo-inflector-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.14.2\jackson-dataformat-yaml-2.14.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-ui\1.8.0\springdoc-openapi-webflux-ui-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webflux-core\1.8.0\springdoc-openapi-webflux-core-1.8.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-maven-plugin\1.5\springdoc-openapi-maven-plugin-1.5.jar;D:\java\IntelliJ IDEA 2025\lib\idea_rt.jar" com.deb.spl.control.Application
2025-09-16 13:35:45.247  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_LEVEL=23
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OTEL_TRACES_EXPORTER=otlp
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : SESSIONNAME=Console
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ALLUSERSPROFILE=C:\ProgramData
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_ARCHITECTURE=AMD64
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : SystemDrive=C:
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_3789132940=1
2025-09-16 13:35:45.248  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : MAVEN_HOME=D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3
2025-09-16 13:35:45.249  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : USERNAME=admin
2025-09-16 13:35:45.249  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles(x86)=C:\Program Files (x86)
2025-09-16 13:35:45.249  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_USER_PROFILE_STRING=Default
2025-09-16 13:35:45.249  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
2025-09-16 13:35:45.249  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : DriverData=C:\Windows\System32\Drivers\DriverData
2025-09-16 13:35:45.249  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : GOPATH=C:\Users\<USER>\go
2025-09-16 13:35:45.250  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ProgramData=C:\ProgramData
2025-09-16 13:35:45.250  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ProgramW6432=C:\Program Files
2025-09-16 13:35:45.250  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : HOMEPATH=\Users\admin
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ProgramFiles=C:\Program Files
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PUBLIC=C:\Users\<USER>\WINDOWS
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : =::=::\
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : LOCALAPPDATA=C:\Users\<USER>\AppData\Local
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyLastPathUpdate=133683072740580159
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : IntelliJ IDEA=D:\java\IntelliJ IDEA 2025\bin;
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : USERDOMAIN=WIN-ARR4C6ROFQD
2025-09-16 13:35:45.251  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
2025-09-16 13:35:45.252  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : LOGONSERVER=\\WIN-ARR4C6ROFQD
2025-09-16 13:35:45.252  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : JAVA_HOME=C:\Program Files\Java\jdk-********
2025-09-16 13:35:45.252  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OMP_WAIT_POLICY=PASSIVE
2025-09-16 13:35:45.252  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OneDrive=C:\Users\<USER>\OneDrive
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : APPDATA=C:\Users\<USER>\AppData\Roaming
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : VBOX_HWVIRTEX_IGNORE_SVM_IN_USE=1
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1262719628=1
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : KMP_BLOCKTIME=0
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ChocolateyInstall=C:\ProgramData\chocolatey
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : VBOX_MSI_INSTALL_PATH=C:\Program Files\Oracle\VirtualBox\
2025-09-16 13:35:45.253  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles=C:\Program Files\Common Files
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : Path=C:\Python313\Scripts\;C:\Python313\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;D:\Program Files\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven3\bin;C:\Program Files\Java\jdk-********;D:\minikube;C:\ProgramData\chocolatey\bin;C:\Program Files\OpenSSH-Win64;D:\java\kafka_2.13-3.8.0\bin\windows;D:\Program Files\Go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;F:\JAVA\apache-tomcat-10.1.36\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;D:\Program Files\PuTTY\;D:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Common Files\Acronis\SnapAPI\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;D:\Program Files\Microsoft VS Code\bin;D:\java\IntelliJ IDEA 2025\bin;;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Program Files\mongosh\;
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2283032206=1
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_2775293581=1
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : OS=Windows_NT
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : COMPUTERNAME=WIN-ARR4C6ROFQD
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : GEMINI_API_KEY=AIzaSyD7bqoJltCqbzJ2BrqIG6mI11l-e9PtiXE
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : CATALINA_HOME=F:\JAVA\apache-tomcat-10.1.36
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : NVM_HOME=C:\Users\<USER>\AppData\Roaming\nvm
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : PROCESSOR_REVISION=6801
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramW6432=C:\Program Files\Common Files
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ComSpec=C:\WINDOWS\system32\cmd.exe
2025-09-16 13:35:45.254  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : SystemRoot=C:\WINDOWS
2025-09-16 13:35:45.255  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : TEMP=C:\Users\<USER>\AppData\Local\Temp
2025-09-16 13:35:45.255  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : EFC_10768_1592913036=1
2025-09-16 13:35:45.255  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : HOMEDRIVE=C:
2025-09-16 13:35:45.255  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : USERPROFILE=C:\Users\<USER>\Users\admin\AppData\Local\Temp
2025-09-16 13:35:45.256  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
2025-09-16 13:35:45.256  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : NUMBER_OF_PROCESSORS=12
2025-09-16 13:35:45.256  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : host.url localhost
2025-09-16 13:35:45.256  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ccv.rocket-status-endpoint.url http://************/api/asku/status/
2025-09-16 13:35:45.256  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : ccv.token spl12345
2025-09-16 13:35:45.258  INFO 23676 --- [restartedMain] com.deb.spl.control.Application          : loaded
2025-09-16 13:35:45.582  INFO 23676 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:35:45.585  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:35:47.563  INFO 23676 --- [http-nio-8079-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 13:35:47.563  INFO 23676 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-16 13:35:47.570  INFO 23676 --- [http-nio-8079-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-09-16 13:35:47.921  WARN 23676 --- [http-nio-8079-exec-1] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [149] milliseconds.
2025-09-16 13:35:47.940  INFO 23676 --- [http-nio-8079-exec-1] c.vaadin.flow.spring.SpringInstantiator  : The number of beans implementing 'I18NProvider' is 0. Cannot use Spring beans for I18N, falling back to the default behavior
2025-09-16 13:35:48.562 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-16 13:35:48.571 ERROR 23676 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-16 13:35:48.591 ERROR 23676 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-16T13:35:48.590399500
2025-09-16 13:35:48.594  INFO 23676 --- [task-2] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:35:48.611 ERROR 23676 --- [task-2] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:36:38.485  WARN 23676 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m34s673ms296µs400ns).
2025-09-16 13:36:38.493  INFO 23676 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-16 13:36:38.495 ERROR 23676 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:123), reactor.core.publisher.Mono.block(Mono.java:1766), com.deb.spl.control.service.CcvCommunicationService.lambda$loadAppSettingsResource$10(CcvCommunicationService.java:245), java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768), java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java), java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760), java.base/java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:373), java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java), java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182), java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655), java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622), java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)]
2025-09-16 13:36:50.049  INFO 23676 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   VITE v3.2.5  ready in 8774 ms
2025-09-16 13:36:50.050 ERROR 23676 --- [task-1] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1960), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2095), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:36:50.051 ERROR 23676 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:36:50.066  INFO 23676 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-16 13:36:50.067  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.v.b.devserver.AbstractDevServerRunner  : Started Vite. Time: 70040ms
2025-09-16 13:36:50.067  INFO 23676 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   :   ➜  Local:   http://127.0.0.1:3660/VAADIN/
2025-09-16 13:36:50.067  INFO 23676 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : 
2025-09-16 13:36:50.068  INFO 23676 --- [dev-server-output] c.v.b.devserver.DevServerOutputTracker   : [TypeScript] Found 0 errors. Watching for file changes.
2025-09-16 13:37:04.587  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:37:04.604 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-16 13:37:04.609  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:37:04.618 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-16 13:37:04.619 ERROR 23676 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-16 13:37:04.620 ERROR 23676 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-16 13:37:04.622  INFO 23676 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:37:04.622  INFO 23676 --- [task-9] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:37:04.622  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:37:04.632 ERROR 23676 --- [task-9] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:37:06.499 ERROR 23676 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-16T13:37:06.499702200
2025-09-16 13:37:17.799  INFO 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:37:19.667 ERROR 23676 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-16T13:37:19.667571900
2025-09-16 13:37:24.202 ERROR 23676 --- [scheduling-1] c.d.spl.control.service.asku.PlcService  : lost connection with plc 2025-09-16T13:37:24.202602300
2025-09-16 13:37:25.686 ERROR 23676 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:37:25.687 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:37:25.687 ERROR 23676 --- [ForkJoinPool.commonPool-worker-2] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:37:25.687 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:37:38.854 ERROR 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:37:38.858 ERROR 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:37:45.077  INFO 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:37:49.497  INFO 23676 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/suto/; 	 requestQueryString null; 	 payload {
  "id": 14084,
  "status": "OK",
  "stats": {
    "roll": 0,
    "pitch": 0,
    "armLiftStrokePosition": 0,
    "levelingCyclesCount": 0,
    "pressureInImpulseSection": 0.0,
    "temperatureRR": 0,
    "workingFluidLevel": 0,
    "mainPumpRPM": 0,
    "overallOperatingTime": 0,
    "overallArmLiftingsCount": 0
  },
  "leftFrontOutriggerEmergencyCode": 0,
  "rightFrontOutriggerEmergencyCode": 0,
  "leftRearOutriggerEmergencyCode": 0,
  "rightRearOutriggerEmergencyCode": 0,
  "properties": [
    {
      "id": 1,
      "name": "chassisHorizontalAlignment",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 2,
      "name": "isChassisHorizontal",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 3,
      "name": "isAlignmentImpossible",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 4,
      "name": "isOutriggersMovingToPP",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 5,
      "name": "isOutriggersInMobileState",
      "state": "ON",
      "propertyType": "REGULAR"
    },
    {
      "id": 6,
      "name": "isEmergencyHangingAndAlignment",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 7,
      "name": "leftFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 8,
      "name": "rightFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 9,
      "name": "rightRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 10,
      "name": "leftRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 11,
      "name": "armUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 12,
      "name": "armRaising",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 13,
      "name": "armRaised",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 14,
      "name": "leftGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 15,
      "name": "rightGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 16,
      "name": "isLoweringLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 17,
      "name": "isLoweringRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 18,
      "name": "emergencySituationForArmAndGasSprings",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 19,
      "name": "leftGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 20,
      "name": "rightGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 21,
      "name": "isRaisingLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 22,
      "name": "isRaisingRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 23,
      "name": "leftGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 24,
      "name": "rightGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 25,
      "name": "leftGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 26,
      "name": "rightGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 27,
      "name": "isLoweringArm",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 28,
      "name": "armInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 29,
      "name": "boomLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 30,
      "name": "boomInEndPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 31,
      "name": "boomEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 32,
      "name": "boomLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 33,
      "name": "leftGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 34,
      "name": "rightGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 35,
      "name": "malfunctionLeftGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 36,
      "name": "malfunctionRightGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 37,
      "name": "armEmptyStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 38,
      "name": "armPistonStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 39,
      "name": "SPLEngineStarting",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 40,
      "name": "SPLEngineStarted",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 41,
      "name": "mainPumpConnectionToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 42,
      "name": "mainPumpConnectedToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 43,
      "name": "unloadingElectromagnetEnabled",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 44,
      "name": "SPLEnginestopping",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 45,
      "name": "emergencySituation",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 46,
      "name": "engineStartImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 47,
      "name": "engineStartImpossible2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 48,
      "name": "engineStopImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 49,
      "name": "fire",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 50,
      "name": "pollutedFilterDZF1",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 51,
      "name": "pollutedFilterDZF2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 52,
      "name": "airPressureNotNormal",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 53,
      "name": "leftFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 54,
      "name": "leftFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 55,
      "name": "rightFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 56,
      "name": "rightFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 57,
      "name": "rightRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 58,
      "name": "rightRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 59,
      "name": "leftRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 60,
      "name": "leftRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 61,
      "name": "leftFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 62,
      "name": "leftFrontLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 63,
      "name": "rightFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 64,
      "name": "rightFrontLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 65,
      "name": "rightRearLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 66,
      "name": "rightRearLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 67,
      "name": "leftRearLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 68,
      "name": "leftRearLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 69,
      "name": "SUTOStop",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    }
  ]
}
2025-09-16 13:37:49.527  INFO 23676 --- [http-nio-8079-exec-7] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=63564, createdAt=2025-09-16T13:37:49.512897400, direction=IN, adjacentSystem=SUTO, method='null', url='http://localhost:8079/api/v1/adjacent-systems/suto/', cachedPayload='{
  "id": 14084,
  "status": "OK",
  "stats": {
    "roll": 0,
    "pitch": 0,
    "armLiftStrokePosition": 0,
    "levelingCyclesCount": 0,
    "pressureInImpulseSection": 0.0,
    "temperatureRR": 0,
    "workingFluidLevel": 0,
    "mainPumpRPM": 0,
    "overallOperatingTime": 0,
    "overallArmLiftingsCount": 0
  },
  "leftFrontOutriggerEmergencyCode": 0,
  "rightFrontOutriggerEmergencyCode": 0,
  "leftRearOutriggerEmergencyCode": 0,
  "rightRearOutriggerEmergencyCode": 0,
  "properties": [
    {
      "id": 1,
      "name": "chassisHorizontalAlignment",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 2,
      "name": "isChassisHorizontal",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 3,
      "name": "isAlignmentImpossible",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 4,
      "name": "isOutriggersMovingToPP",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 5,
      "name": "isOutriggersInMobileState",
      "state": "ON",
      "propertyType": "REGULAR"
    },
    {
      "id": 6,
      "name": "isEmergencyHangingAndAlignment",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 7,
      "name": "leftFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 8,
      "name": "rightFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 9,
      "name": "rightRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 10,
      "name": "leftRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 11,
      "name": "armUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 12,
      "name": "armRaising",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 13,
      "name": "armRaised",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 14,
      "name": "leftGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 15,
      "name": "rightGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 16,
      "name": "isLoweringLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 17,
      "name": "isLoweringRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 18,
      "name": "emergencySituationForArmAndGasSprings",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 19,
      "name": "leftGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 20,
      "name": "rightGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 21,
      "name": "isRaisingLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 22,
      "name": "isRaisingRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 23,
      "name": "leftGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 24,
      "name": "rightGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 25,
      "name": "leftGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 26,
      "name": "rightGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 27,
      "name": "isLoweringArm",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 28,
      "name": "armInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 29,
      "name": "boomLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 30,
      "name": "boomInEndPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 31,
      "name": "boomEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 32,
      "name": "boomLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 33,
      "name": "leftGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 34,
      "name": "rightGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 35,
      "name": "malfunctionLeftGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 36,
      "name": "malfunctionRightGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 37,
      "name": "armEmptyStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 38,
      "name": "armPistonStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 39,
      "name": "SPLEngineStarting",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 40,
      "name": "SPLEngineStarted",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 41,
      "name": "mainPumpConnectionToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 42,
      "name": "mainPumpConnectedToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 43,
      "name": "unloadingElectromagnetEnabled",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 44,
      "name": "SPLEnginestopping",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 45,
      "name": "emergencySituation",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 46,
      "name": "engineStartImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 47,
      "name": "engineStartImpossible2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 48,
      "name": "engineStopImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 49,
      "name": "fire",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 50,
      "name": "pollutedFilterDZF1",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 51,
      "name": "pollutedFilterDZF2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 52,
      "name": "airPressureNotNormal",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 53,
      "name": "leftFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 54,
      "name": "leftFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 55,
      "name": "rightFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 56,
      "name": "rightFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 57,
      "name": "rightRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 58,
      "name": "rightRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 59,
      "name": "leftRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 60,
      "name": "leftRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 61,
      "name": "leftFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "R', requestQueryString='null', headers=null}
2025-09-16 13:38:07.010 ERROR 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:38:07.011 ERROR 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:38:36.977  WARN 23676 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=50s181ms311µs100ns).
2025-09-16 13:38:37.071  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:38:37.081 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-16 13:38:37.084  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:38:37.094 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-16 13:38:37.095 ERROR 23676 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-16 13:38:37.232 ERROR 23676 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-16 13:38:37.233  INFO 23676 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:38:37.234  INFO 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:38:37.255 ERROR 23676 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:38:45.072  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:38:48.138  INFO 23676 --- [http-nio-8079-exec-2] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/suto/; 	 requestQueryString null; 	 payload {
  "id": 14084,
  "status": "OK",
  "stats": {
    "roll": 0,
    "pitch": 0,
    "armLiftStrokePosition": 0,
    "levelingCyclesCount": 0,
    "pressureInImpulseSection": 0.0,
    "temperatureRR": 0,
    "workingFluidLevel": 0,
    "mainPumpRPM": 0,
    "overallOperatingTime": 0,
    "overallArmLiftingsCount": 0
  },
  "leftFrontOutriggerEmergencyCode": 0,
  "rightFrontOutriggerEmergencyCode": 0,
  "leftRearOutriggerEmergencyCode": 0,
  "rightRearOutriggerEmergencyCode": 0,
  "properties": [
    {
      "id": 1,
      "name": "chassisHorizontalAlignment",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 2,
      "name": "isChassisHorizontal",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 3,
      "name": "isAlignmentImpossible",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 4,
      "name": "isOutriggersMovingToPP",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 5,
      "name": "isOutriggersInMobileState",
      "state": "ON",
      "propertyType": "REGULAR"
    },
    {
      "id": 6,
      "name": "isEmergencyHangingAndAlignment",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 7,
      "name": "leftFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 8,
      "name": "rightFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 9,
      "name": "rightRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 10,
      "name": "leftRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 11,
      "name": "armUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 12,
      "name": "armRaising",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 13,
      "name": "armRaised",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 14,
      "name": "leftGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 15,
      "name": "rightGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 16,
      "name": "isLoweringLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 17,
      "name": "isLoweringRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 18,
      "name": "emergencySituationForArmAndGasSprings",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 19,
      "name": "leftGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 20,
      "name": "rightGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 21,
      "name": "isRaisingLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 22,
      "name": "isRaisingRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 23,
      "name": "leftGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 24,
      "name": "rightGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 25,
      "name": "leftGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 26,
      "name": "rightGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 27,
      "name": "isLoweringArm",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 28,
      "name": "armInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 29,
      "name": "boomLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 30,
      "name": "boomInEndPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 31,
      "name": "boomEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 32,
      "name": "boomLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 33,
      "name": "leftGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 34,
      "name": "rightGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 35,
      "name": "malfunctionLeftGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 36,
      "name": "malfunctionRightGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 37,
      "name": "armEmptyStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 38,
      "name": "armPistonStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 39,
      "name": "SPLEngineStarting",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 40,
      "name": "SPLEngineStarted",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 41,
      "name": "mainPumpConnectionToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 42,
      "name": "mainPumpConnectedToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 43,
      "name": "unloadingElectromagnetEnabled",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 44,
      "name": "SPLEnginestopping",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 45,
      "name": "emergencySituation",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 46,
      "name": "engineStartImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 47,
      "name": "engineStartImpossible2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 48,
      "name": "engineStopImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 49,
      "name": "fire",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 50,
      "name": "pollutedFilterDZF1",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 51,
      "name": "pollutedFilterDZF2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 52,
      "name": "airPressureNotNormal",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 53,
      "name": "leftFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 54,
      "name": "leftFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 55,
      "name": "rightFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 56,
      "name": "rightFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 57,
      "name": "rightRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 58,
      "name": "rightRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 59,
      "name": "leftRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 60,
      "name": "leftRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 61,
      "name": "leftFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 62,
      "name": "leftFrontLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 63,
      "name": "rightFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 64,
      "name": "rightFrontLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 65,
      "name": "rightRearLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 66,
      "name": "rightRearLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 67,
      "name": "leftRearLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 68,
      "name": "leftRearLockRightTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 69,
      "name": "SUTOStop",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    }
  ]
}
2025-09-16 13:38:48.160  INFO 23676 --- [http-nio-8079-exec-2] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=63565, createdAt=2025-09-16T13:38:48.144335200, direction=IN, adjacentSystem=SUTO, method='null', url='http://localhost:8079/api/v1/adjacent-systems/suto/', cachedPayload='{
  "id": 14084,
  "status": "OK",
  "stats": {
    "roll": 0,
    "pitch": 0,
    "armLiftStrokePosition": 0,
    "levelingCyclesCount": 0,
    "pressureInImpulseSection": 0.0,
    "temperatureRR": 0,
    "workingFluidLevel": 0,
    "mainPumpRPM": 0,
    "overallOperatingTime": 0,
    "overallArmLiftingsCount": 0
  },
  "leftFrontOutriggerEmergencyCode": 0,
  "rightFrontOutriggerEmergencyCode": 0,
  "leftRearOutriggerEmergencyCode": 0,
  "rightRearOutriggerEmergencyCode": 0,
  "properties": [
    {
      "id": 1,
      "name": "chassisHorizontalAlignment",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 2,
      "name": "isChassisHorizontal",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 3,
      "name": "isAlignmentImpossible",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 4,
      "name": "isOutriggersMovingToPP",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 5,
      "name": "isOutriggersInMobileState",
      "state": "ON",
      "propertyType": "REGULAR"
    },
    {
      "id": 6,
      "name": "isEmergencyHangingAndAlignment",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 7,
      "name": "leftFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 8,
      "name": "rightFrontOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 9,
      "name": "rightRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 10,
      "name": "leftRearOutriggerInitialPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 11,
      "name": "armUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 12,
      "name": "armRaising",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 13,
      "name": "armRaised",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 14,
      "name": "leftGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 15,
      "name": "rightGasSpringUnlocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 16,
      "name": "isLoweringLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 17,
      "name": "isLoweringRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 18,
      "name": "emergencySituationForArmAndGasSprings",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 19,
      "name": "leftGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 20,
      "name": "rightGasSpringLowered",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 21,
      "name": "isRaisingLeftGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 22,
      "name": "isRaisingRightGasSpring",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 23,
      "name": "leftGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 24,
      "name": "rightGasSpringInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 25,
      "name": "leftGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 26,
      "name": "rightGasSpringLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 27,
      "name": "isLoweringArm",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 28,
      "name": "armInMobileState",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 29,
      "name": "boomLocked",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 30,
      "name": "boomInEndPosition",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 31,
      "name": "boomEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 32,
      "name": "boomLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 33,
      "name": "leftGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 34,
      "name": "rightGasSpringLockingEPMalfunction",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 35,
      "name": "malfunctionLeftGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 36,
      "name": "malfunctionRightGasReflectorEP",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 37,
      "name": "armEmptyStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 38,
      "name": "armPistonStrokeMaxPressure",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 39,
      "name": "SPLEngineStarting",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 40,
      "name": "SPLEngineStarted",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 41,
      "name": "mainPumpConnectionToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 42,
      "name": "mainPumpConnectedToGS_N1",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 43,
      "name": "unloadingElectromagnetEnabled",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 44,
      "name": "SPLEnginestopping",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 45,
      "name": "emergencySituation",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 46,
      "name": "engineStartImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 47,
      "name": "engineStartImpossible2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 48,
      "name": "engineStopImpossible",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 49,
      "name": "fire",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 50,
      "name": "pollutedFilterDZF1",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 51,
      "name": "pollutedFilterDZF2",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 52,
      "name": "airPressureNotNormal",
      "state": "OFF",
      "propertyType": "MALFUNCTION"
    },
    {
      "id": 53,
      "name": "leftFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 54,
      "name": "leftFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 55,
      "name": "rightFrontLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 56,
      "name": "rightFrontLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 57,
      "name": "rightRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 58,
      "name": "rightRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 59,
      "name": "leftRearLockLeftTPKClosed",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 60,
      "name": "leftRearLockLeftTPKOpened",
      "state": "OFF",
      "propertyType": "REGULAR"
    },
    {
      "id": 61,
      "name": "leftFrontLockRightTPKClosed",
      "state": "OFF",
      "propertyType": "R', requestQueryString='null', headers=null}
2025-09-16 13:38:58.294 ERROR 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:38:58.294 ERROR 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:39:06.104 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:39:06.105 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:39:15.074  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:39:22.613  INFO 23676 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : REQUEST DATA: request URL http://localhost:8079/api/v1/adjacent-systems/suto/; 	 requestQueryString null; 	 payload {
    "id": 78400,
    "status": "OK",
    "stats": {
        "roll": 0,
        "pitch": 0,
        "armLiftStrokePosition": 0,
        "levelingCyclesCount": 0,
        "pressureInImpulseSection": 0.0,
        "temperatureRR": 0,
        "workingFluidLevel": 0,
        "mainPumpRPM": 0,
        "overallOperatingTime": 0,
        "overallArmLiftingsCount": 0
    },
    "leftFrontOutriggerEmergencyCode": 0,
    "rightFrontOutriggerEmergencyCode": 0,
    "leftRearOutriggerEmergencyCode": 0,
    "rightRearOutriggerEmergencyCode": 0,
    "properties": [
        {
            "id": 5,
            "name": "isOutriggersInMobileState",
            "state": "ON",
            "propertyType": "REGULAR"
        },
        {
            "id": 6,
            "name": "isEmergencyHangingAndAlignment",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 7,
            "name": "leftFrontOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 8,
            "name": "rightFrontOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 9,
            "name": "rightRearOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 10,
            "name": "leftRearOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 11,
            "name": "armUnlocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 12,
            "name": "armRaising",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 13,
            "name": "armRaised",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 14,
            "name": "leftGasSpringUnlocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 15,
            "name": "rightGasSpringUnlocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 16,
            "name": "isLoweringLeftGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 17,
            "name": "isLoweringRightGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 18,
            "name": "emergencySituationForArmAndGasSprings",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 19,
            "name": "leftGasSpringLowered",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 20,
            "name": "rightGasSpringLowered",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 21,
            "name": "isRaisingLeftGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 22,
            "name": "isRaisingRightGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 23,
            "name": "leftGasSpringInMobileState",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 24,
            "name": "rightGasSpringInMobileState",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 25,
            "name": "leftGasSpringLocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 26,
            "name": "rightGasSpringLocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 27,
            "name": "isLoweringArm",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 28,
            "name": "armInMobileState",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 29,
            "name": "boomLocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 30,
            "name": "boomInEndPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 31,
            "name": "boomEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 32,
            "name": "boomLockingEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 33,
            "name": "leftGasSpringLockingEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 34,
            "name": "rightGasSpringLockingEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 35,
            "name": "malfunctionLeftGasReflectorEP",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 36,
            "name": "malfunctionRightGasReflectorEP",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 37,
            "name": "armEmptyStrokeMaxPressure",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 38,
            "name": "armPistonStrokeMaxPressure",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 39,
            "name": "SPLEngineStarting",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 40,
            "name": "SPLEngineStarted",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 41,
            "name": "mainPumpConnectionToGS_N1",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 42,
            "name": "mainPumpConnectedToGS_N1",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 43,
            "name": "unloadingElectromagnetEnabled",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 44,
            "name": "SPLEnginestopping",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 45,
            "name": "emergencySituation",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 46,
            "name": "engineStartImpossible",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 47,
            "name": "engineStartImpossible2",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 48,
            "name": "engineStopImpossible",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 49,
            "name": "fire",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 50,
            "name": "pollutedFilterDZF1",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 51,
            "name": "pollutedFilterDZF2",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 52,
            "name": "airPressureNotNormal",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 53,
            "name": "leftFrontLockLeftTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 54,
            "name": "leftFrontLockLeftTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 55,
            "name": "rightFrontLockLeftTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 56,
            "name": "rightFrontLockLeftTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 57,
            "name": "rightRearLockLeftTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 58,
            "name": "rightRearLockLeftTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 59,
            "name": "leftRearLockLeftTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 60,
            "name": "leftRearLockLeftTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 61,
            "name": "leftFrontLockRightTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 62,
            "name": "leftFrontLockRightTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 63,
            "name": "rightFrontLockRightTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 64,
            "name": "rightFrontLockRightTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 65,
            "name": "rightRearLockRightTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 66,
            "name": "rightRearLockRightTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 67,
            "name": "leftRearLockRightTPKClosed",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 68,
            "name": "leftRearLockRightTPKOpened",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 69,
            "name": "SUTOStop",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        }
    ]
}
2025-09-16 13:39:22.639  INFO 23676 --- [http-nio-8079-exec-4] c.d.s.c.utils.logs.RequestCachingFilter  : RestRequest{id=63566, createdAt=2025-09-16T13:39:22.618809800, direction=IN, adjacentSystem=SUTO, method='null', url='http://localhost:8079/api/v1/adjacent-systems/suto/', cachedPayload='{
    "id": 78400,
    "status": "OK",
    "stats": {
        "roll": 0,
        "pitch": 0,
        "armLiftStrokePosition": 0,
        "levelingCyclesCount": 0,
        "pressureInImpulseSection": 0.0,
        "temperatureRR": 0,
        "workingFluidLevel": 0,
        "mainPumpRPM": 0,
        "overallOperatingTime": 0,
        "overallArmLiftingsCount": 0
    },
    "leftFrontOutriggerEmergencyCode": 0,
    "rightFrontOutriggerEmergencyCode": 0,
    "leftRearOutriggerEmergencyCode": 0,
    "rightRearOutriggerEmergencyCode": 0,
    "properties": [
        {
            "id": 5,
            "name": "isOutriggersInMobileState",
            "state": "ON",
            "propertyType": "REGULAR"
        },
        {
            "id": 6,
            "name": "isEmergencyHangingAndAlignment",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 7,
            "name": "leftFrontOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 8,
            "name": "rightFrontOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 9,
            "name": "rightRearOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 10,
            "name": "leftRearOutriggerInitialPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 11,
            "name": "armUnlocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 12,
            "name": "armRaising",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 13,
            "name": "armRaised",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 14,
            "name": "leftGasSpringUnlocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 15,
            "name": "rightGasSpringUnlocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 16,
            "name": "isLoweringLeftGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 17,
            "name": "isLoweringRightGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 18,
            "name": "emergencySituationForArmAndGasSprings",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 19,
            "name": "leftGasSpringLowered",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 20,
            "name": "rightGasSpringLowered",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 21,
            "name": "isRaisingLeftGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 22,
            "name": "isRaisingRightGasSpring",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 23,
            "name": "leftGasSpringInMobileState",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 24,
            "name": "rightGasSpringInMobileState",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 25,
            "name": "leftGasSpringLocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 26,
            "name": "rightGasSpringLocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 27,
            "name": "isLoweringArm",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 28,
            "name": "armInMobileState",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 29,
            "name": "boomLocked",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 30,
            "name": "boomInEndPosition",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 31,
            "name": "boomEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 32,
            "name": "boomLockingEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 33,
            "name": "leftGasSpringLockingEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 34,
            "name": "rightGasSpringLockingEPMalfunction",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 35,
            "name": "malfunctionLeftGasReflectorEP",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 36,
            "name": "malfunctionRightGasReflectorEP",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 37,
            "name": "armEmptyStrokeMaxPressure",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 38,
            "name": "armPistonStrokeMaxPressure",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 39,
            "name": "SPLEngineStarting",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 40,
            "name": "SPLEngineStarted",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 41,
            "name": "mainPumpConnectionToGS_N1",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 42,
            "name": "mainPumpConnectedToGS_N1",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 43,
            "name": "unloadingElectromagnetEnabled",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 44,
            "name": "SPLEnginestopping",
            "state": "OFF",
            "propertyType": "REGULAR"
        },
        {
            "id": 45,
            "name": "emergencySituation",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 46,
            "name": "engineStartImpossible",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 47,
            "name": "engineStartImpossible2",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 48,
            "name": "engineStopImpossible",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 49,
            "name": "fire",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 50,
            "name": "pollutedFilterDZF1",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 51,
            "name": "pollutedFilterDZF2",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "id": 52,
            "name": "airPressureNotNormal",
            "state": "OFF",
            "propertyType": "MALFUNCTION"
        },
        {
            "', requestQueryString='null', headers=null}
2025-09-16 13:39:36.115 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:39:36.115 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:39:37.292  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:39:37.297 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-16 13:39:37.298  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:39:37.302 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-16 13:39:37.302 ERROR 23676 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-16 13:39:37.417 ERROR 23676 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-16 13:39:37.417  INFO 23676 --- [task-8] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:39:37.421 ERROR 23676 --- [task-8] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:39:45.133  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:41:04.823  WARN 23676 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m27s829ms438µs100ns).
2025-09-16 13:41:04.824 ERROR 23676 --- [task-4] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1960), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2095), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:41:04.824 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : [reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:123), reactor.core.publisher.Mono.block(Mono.java:1766), com.deb.spl.control.service.CcvCommunicationService.lambda$loadAppSettingsResource$10(CcvCommunicationService.java:245), java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768), java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java), java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760), java.base/java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:373), java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java), java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182), java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655), java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622), java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)]
2025-09-16 13:41:04.824 ERROR 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : can't get AppSettingsResource: urlhttp://*************/api/app_settings failed with exception.
2025-09-16 13:41:04.863  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins/utc', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:41:04.872 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : bins is disconnected for 60 sec.
2025-09-16 13:41:04.874  INFO 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=BINS, method='GET', url='http://localhost:8081/bins', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:41:04.884 ERROR 23676 --- [scheduling-1] com.deb.spl.control.service.BinsService  : can't connect to http://localhost:8081/bins The reason is  Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
2025-09-16 13:41:04.885 ERROR 23676 --- [scheduling-1] c.d.s.c.service.BinsTaskExecutorService  : bins is disconnected for 60 sec.
2025-09-16 13:41:05.265 ERROR 23676 --- [scheduling-1] c.d.s.c.service.MsuTaskExecutorService   : MSU is disconnected for 60 sec.
2025-09-16 13:41:05.266  INFO 23676 --- [task-16] com.deb.spl.control.service.MsuService   : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=MSU, method='GET', url='http://localhost:8081/msu', cachedPayload='', requestQueryString='null', headers=null}
2025-09-16 13:41:05.266  INFO 23676 --- [ForkJoinPool.commonPool-worker-1] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:41:05.266  INFO 23676 --- [ForkJoinPool.commonPool-worker-3] c.d.s.c.service.CcvCommunicationService  : RestRequest{id=null, createdAt=null, direction=OUT, adjacentSystem=CCV, method='GET', url='http://*************/api/app_settings', cachedPayload='', requestQueryString='null', headers=[{Token} = {spl12345}, {Accept} = {application/json}]}
2025-09-16 13:41:05.326 ERROR 23676 --- [task-16] com.deb.spl.control.service.MsuService   : Connection refused: no further information: localhost/127.0.0.1:8081; nested exception is io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:8081
[org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:141)]
2025-09-16 13:41:07.330 ERROR 23676 --- [task-4] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:41:07.330 ERROR 23676 --- [task-10] c.d.s.c.service.CcvCommunicationService  : null [java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:386), java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096), com.deb.spl.control.service.CcvCommunicationService.updateMasterCcvConnectionState(CcvCommunicationService.java:370), com.deb.spl.control.service.CcvCommunicationService$$FastClassBySpringCGLIB$$387eb832.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763), org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115), java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264), java.base/java.util.concurrent.FutureTask.run(FutureTask.java), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:858)]
2025-09-16 13:41:07.376  INFO 23676 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-16 13:41:07.397  INFO 23676 --- [scheduling-1] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-09-16 13:41:07.401  INFO 23676 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-16 13:41:07.423  INFO 23676 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
