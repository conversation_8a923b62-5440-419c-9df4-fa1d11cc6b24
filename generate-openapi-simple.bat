@echo off
echo Simple OpenAPI YAML generation...
echo.

echo Step 1: Starting Spring Boot application in background...
start /B mvn spring-boot:run > app.log 2>&1

echo Waiting for application to start (30 seconds)...
timeout /t 30 /nobreak > nul

echo Step 2: Checking if application is ready...
curl -s -o nul -w "%%{http_code}" http://localhost:8079/api-docs > temp_status.txt
set /p status=<temp_status.txt
del temp_status.txt

if "%status%"=="200" (
    echo ✓ Application is ready!
    
    echo Step 3: Fetching OpenAPI spec and saving as YAML...
    mkdir target\generated-sources\openapi 2>nul
    curl -s http://localhost:8079/api-docs > target\generated-sources\openapi\openapi.json
    
    echo Step 4: Converting JSON to YAML format...
    REM Note: This saves as JSON for now. For true YAML, you'd need yq or similar tool
    copy target\generated-sources\openapi\openapi.json target\generated-sources\openapi\openapi.yml >nul
    
    echo Step 5: Copying to docker directory...
    copy target\generated-sources\openapi\openapi.yml docker\spl_control\openapi.yml >nul
    copy target\generated-sources\openapi\openapi.yml openapi.yml >nul
    
    echo ✓ Files generated:
    echo   - target\generated-sources\openapi\openapi.yml
    echo   - docker\spl_control\openapi.yml  
    echo   - openapi.yml
    
) else (
    echo ✗ Application not ready. Status: %status%
)

echo Step 6: Stopping Spring Boot application...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "mvn\|spring"') do (
    taskkill /F /PID %%i 2>nul
)

echo.
echo Done! Press any key to exit.
pause >nul
