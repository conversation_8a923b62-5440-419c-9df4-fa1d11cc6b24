@echo off
echo Generating OpenAPI YAML using test approach...
echo.

echo Step 1: Running OpenAPI generator test...
mvn test -Dtest=OpenApiGeneratorTest

echo.
if exist "target\generated-sources\openapi\openapi.yml" (
    echo ✓ OpenAPI YAML files generated successfully!
    echo.
    echo Generated files:
    echo   ✓ target\generated-sources\openapi\openapi.yml
    echo   ✓ target\generated-sources\openapi\openapi.json
    if exist "docker\spl_control\openapi.yml" (
        echo   ✓ docker\spl_control\openapi.yml
        echo   ✓ docker\spl_control\openapi.json
    )
    if exist "openapi.yml" (
        echo   ✓ openapi.yml (root directory)
    )
) else (
    echo ✗ Failed to generate OpenAPI YAML files.
    echo Check the test output above for errors.
)

echo.
pause
