@echo off
echo ========================================
echo  Safe OpenAPI Generation Script
echo ========================================

echo Step 1: Cleaning up any existing Java processes...
taskkill /F /IM java.exe 2>nul
timeout /t 2 >nul

echo Step 2: Running Maven integration test with OpenAPI generation...
mvn clean compile -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven compile failed
    goto :cleanup
)

echo Step 3: Starting integration test...
mvn integration-test -Pgenerate-openapi -DskipTests
set MAVEN_EXIT_CODE=%ERRORLEVEL%

echo Step 4: Ensuring all processes are stopped...
timeout /t 3 >nul
taskkill /F /IM java.exe 2>nul

echo Step 5: Verifying OpenAPI files were generated...
if exist "target\generated-sources\openapi\openapi.yml" (
    echo ✓ OpenAPI YAML generated successfully
) else (
    echo ✗ OpenAPI YAML not found
)

if exist "docker\spl_control\openapi.yml" (
    echo ✓ OpenAPI YAML copied to docker directory
) else (
    echo ✗ OpenAPI YAML not copied to docker directory
)

:cleanup
echo Step 6: Final cleanup...
taskkill /F /IM java.exe 2>nul

if %MAVEN_EXIT_CODE% equ 0 (
    echo ========================================
    echo  OpenAPI Generation Completed Successfully!
    echo ========================================
) else (
    echo ========================================
    echo  OpenAPI Generation Failed (Exit Code: %MAVEN_EXIT_CODE%)
    echo ========================================
)

pause
