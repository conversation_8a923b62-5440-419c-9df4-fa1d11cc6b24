{"id": 14084, "status": "OK", "stats": {"roll": 0, "pitch": 0, "armLiftStrokePosition": 0, "levelingCyclesCount": 0, "pressureInImpulseSection": 0.0, "temperatureRR": 0, "workingFluidLevel": 0, "mainPumpRPM": 0, "overallOperatingTime": 0, "overallArmLiftingsCount": 0}, "leftFrontOutriggerEmergencyCode": 0, "rightFrontOutriggerEmergencyCode": 0, "leftRearOutriggerEmergencyCode": 0, "rightRearOutriggerEmergencyCode": 0, "properties": [{"id": 1, "name": "chassisHorizontalAlignment", "state": "OFF", "propertyType": "REGULAR"}, {"id": 2, "name": "isChassisHorizontal", "state": "OFF", "propertyType": "REGULAR"}, {"id": 3, "name": "isAlignmentImpossible", "state": "OFF", "propertyType": "REGULAR"}, {"id": 4, "name": "isOutriggersMovingToPP", "state": "OFF", "propertyType": "REGULAR"}, {"id": 5, "name": "isOutriggersInMobileState", "state": "ON", "propertyType": "REGULAR"}, {"id": 6, "name": "isEmergencyHangingAndAlignment", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 7, "name": "leftFrontOutriggerInitialPosition", "state": "OFF", "propertyType": "REGULAR"}, {"id": 8, "name": "rightFrontOutriggerInitialPosition", "state": "OFF", "propertyType": "REGULAR"}, {"id": 9, "name": "rightRearOutriggerInitialPosition", "state": "OFF", "propertyType": "REGULAR"}, {"id": 10, "name": "leftRearOutriggerInitialPosition", "state": "OFF", "propertyType": "REGULAR"}, {"id": 11, "name": "armUnlocked", "state": "OFF", "propertyType": "REGULAR"}, {"id": 12, "name": "armRaising", "state": "OFF", "propertyType": "REGULAR"}, {"id": 13, "name": "armRaised", "state": "OFF", "propertyType": "REGULAR"}, {"id": 14, "name": "leftGasSpringUnlocked", "state": "OFF", "propertyType": "REGULAR"}, {"id": 15, "name": "rightGasSpringUnlocked", "state": "OFF", "propertyType": "REGULAR"}, {"id": 16, "name": "isLoweringLeftGasSpring", "state": "OFF", "propertyType": "REGULAR"}, {"id": 17, "name": "isLoweringRightGasSpring", "state": "OFF", "propertyType": "REGULAR"}, {"id": 18, "name": "emergencySituationForArmAndGasSprings", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 19, "name": "leftGasSpringLowered", "state": "OFF", "propertyType": "REGULAR"}, {"id": 20, "name": "rightGasSpringLowered", "state": "OFF", "propertyType": "REGULAR"}, {"id": 21, "name": "isRaisingLeftGasSpring", "state": "OFF", "propertyType": "REGULAR"}, {"id": 22, "name": "isRaisingRightGasSpring", "state": "OFF", "propertyType": "REGULAR"}, {"id": 23, "name": "leftGasSpringInMobileState", "state": "OFF", "propertyType": "REGULAR"}, {"id": 24, "name": "rightGasSpringInMobileState", "state": "OFF", "propertyType": "REGULAR"}, {"id": 25, "name": "leftGasSpringLocked", "state": "OFF", "propertyType": "REGULAR"}, {"id": 26, "name": "rightGasSpringLocked", "state": "OFF", "propertyType": "REGULAR"}, {"id": 27, "name": "isLoweringArm", "state": "OFF", "propertyType": "REGULAR"}, {"id": 28, "name": "armInMobileState", "state": "OFF", "propertyType": "REGULAR"}, {"id": 29, "name": "boomLocked", "state": "OFF", "propertyType": "REGULAR"}, {"id": 30, "name": "boomInEndPosition", "state": "OFF", "propertyType": "REGULAR"}, {"id": 31, "name": "boomEPMalfunction", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 32, "name": "boomLockingEPMalfunction", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 33, "name": "leftGasSpringLockingEPMalfunction", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 34, "name": "rightGasSpringLockingEPMalfunction", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 35, "name": "malfunctionLeftGasReflectorEP", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 36, "name": "malfunctionRightGasReflectorEP", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 37, "name": "armEmptyStrokeMaxPressure", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 38, "name": "armPistonStrokeMaxPressure", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 39, "name": "SPLEngineStarting", "state": "OFF", "propertyType": "REGULAR"}, {"id": 40, "name": "SPLEngineStarted", "state": "OFF", "propertyType": "REGULAR"}, {"id": 41, "name": "mainPumpConnectionToGS_N1", "state": "OFF", "propertyType": "REGULAR"}, {"id": 42, "name": "mainPumpConnectedToGS_N1", "state": "OFF", "propertyType": "REGULAR"}, {"id": 43, "name": "unloadingElectromagnetEnabled", "state": "OFF", "propertyType": "REGULAR"}, {"id": 44, "name": "SPLEnginestopping", "state": "OFF", "propertyType": "REGULAR"}, {"id": 45, "name": "emergencySituation", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 46, "name": "engineStartImpossible", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 47, "name": "engineStartImpossible2", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 48, "name": "engineStopImpossible", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 49, "name": "fire", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 50, "name": "pollutedFilterDZF1", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 51, "name": "pollutedFilterDZF2", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 52, "name": "airPressureNotNormal", "state": "OFF", "propertyType": "MALFUNCTION"}, {"id": 53, "name": "leftFrontLockLeftTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 54, "name": "leftFrontLockLeftTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 55, "name": "rightFrontLockLeftTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 56, "name": "rightFrontLockLeftTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 57, "name": "rightRearLockLeftTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 58, "name": "rightRearLockLeftTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 59, "name": "leftRearLockLeftTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 60, "name": "leftRearLockLeftTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 61, "name": "leftFrontLockRightTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 62, "name": "leftFrontLockRightTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 63, "name": "rightFrontLockRightTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 64, "name": "rightFrontLockRightTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 65, "name": "rightRearLockRightTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 66, "name": "rightRearLockRightTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 67, "name": "leftRearLockRightTPKClosed", "state": "OFF", "propertyType": "REGULAR"}, {"id": 68, "name": "leftRearLockRightTPKOpened", "state": "OFF", "propertyType": "REGULAR"}, {"id": 69, "name": "SUTOStop", "state": "OFF", "propertyType": "MALFUNCTION"}]}