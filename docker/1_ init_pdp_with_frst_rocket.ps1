Start-Sleep -Milliseconds 2500

# Define the URL
$url = "http://localhost:8079/api/v1/asku/rocket?isLeft=1"

# Define the request headers
$headers = @{
    "Token" = "plc12345"
}

# Define the request body
$body = @{
    technicalCondition = $true
    dateUseM = $null
    formData = @{
    # "createdAt" = "2023-08-29T18:33:00"
        plantMissile = "11"
        warhead = "CBCH"
        gsnType = "NO_GSN"
        alpType = "NO_ALP"
        isTelemetryIntegrated = $true
        purposeType = "COMBAT"
    }
    initialData = $null
    initialDataTS = $null
    initialDataSource = "MSG"
    initialDataSourceDescription = "description"
    storedTlKeys = @("123", "123---sdfsdfsfd1465ssdfsd====")
} | ConvertTo-Json -Depth 10

# Send the POST request
$response = Invoke-WebRequest -Uri $url -Method Post -Headers $headers -Body $body -ContentType "application/json"


# Define the request body as a nested hashtable
$orderTime = (Get-Date).AddMinutes(15).ToString("yyyy-MM-ddTHH:mm:ss.fffffff")

$body = @{
    launchInitialData = @{
        loadTemperature = -999.9
        latitudeRad = "45.12877000"
        longitudeRad = "31.67633000"
        altitude = 0.0
        inclinationAngle = -75.0
        trajectory = "AERO_BALLISTIC"
        readiness = "BG_2B"
        isProDetected = $true
        missileOperatingMode = "BASE_SNS"
        validatedByTlc = $true
        tlCode = "gvgFYMY3lUTiVvHwj08tlOH+vCjlwHoihjJc9qIIlhyA43zffAG8ezxrzBWr2GJEfqj6c13olcdfQxBPz4ogSQ=="
        scheduled = "true"
        startTime = "2025-07-05T19:30:35.8170186"
    }
    orderInfo = @{
        entityId = "aa29b624-0e2b-4687-a2e6-b268422cdb11"
        validUntil = "2025-07-05T19:30:35.8170186"
    }
}

# Define the request headers
$headers = @{
    Token = "plc12345"
}

# Send the HTTP POST request
Invoke-WebRequest -Uri "http://localhost:8079/api/v1/asku/fire_order/11" `
    -Method POST `
    -Headers $headers `
    -Body ($body | ConvertTo-Json -Depth 10) `
    -ContentType "application/json"

&"$PSScriptroot\order.ps1"
&"$PSScriptroot\pdp_byn_with_ncok_4_lunch.ps1"

# set temperature
$url = "http://localhost:8079/api/v1/asku/rocket/sensors?temperature=15.6&isLeft=true"
$headers = @{ "Token" = "plc12345" }

Invoke-WebRequest -Uri $url -Method Post -Headers $headers

#set BG_2
$url = "http://localhost:8079/api/v1/asku/spl_readiness/BG_2B"
$headers = @{ "Token" = "plc12345" }

Invoke-WebRequest -Uri $url -Method Post -Headers $headers

&"$PSScriptroot\commit_lunch.ps1"

&"$PSScriptroot\start_timer_1.ps1"

Start-Sleep -Milliseconds 5000
&"$PSScriptroot\set_suto_level.ps1"

