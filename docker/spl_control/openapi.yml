{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8079", "description": "Generated server url"}], "paths": {"/api/v1/adjacent-systems/sae/status/system": {"get": {"tags": ["sae-controller"], "operationId": "getSystemStatus", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}}}}}}, "put": {"tags": ["sae-controller"], "operationId": "updateSystemStatus", "parameters": [{"name": "status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SaeDto"}}}}}}}, "/api/v1/adjacent-systems/sae/status/feeders": {"get": {"tags": ["sae-controller"], "operationId": "getFeedersStatuses", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}}}}}}, "put": {"tags": ["sae-controller"], "operationId": "updateFeedersStatuses", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SaeDto"}}}}}}}, "/api/v1/adjacent-systems/sae/status/feeders/{feeder_name}": {"get": {"tags": ["sae-controller"], "operationId": "getFeederStatus", "parameters": [{"name": "feeder_name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MapEntryStringSaeFeederStatus"}}}}}}, "put": {"tags": ["sae-controller"], "operationId": "updateFeederStatus", "parameters": [{"name": "feeder_name", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "feeder_status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MapEntryStringSaeFeederStatus"}}}}}}}, "/api/v1/adjacent-systems/sae/": {"get": {"tags": ["sae-controller"], "operationId": "getSaeDto", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SaeDto"}}}}}}, "put": {"tags": ["sae-controller"], "operationId": "updateSaeDto", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaeDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SaeDto"}}}}}}}, "/api/v1/adjacent-systems/ppo/bay/{bay_type}": {"get": {"tags": ["ppo-controller"], "operationId": "getPpoBayDtoByType", "parameters": [{"name": "bay_type", "in": "path", "required": true, "schema": {"type": "string", "enum": ["ASKU", "NPPA", "DEA", "TO", "UNDEFINED"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoBayDTO"}}}}}}, "put": {"tags": ["ppo-controller"], "operationId": "updateByBayType", "parameters": [{"name": "bay_type", "in": "path", "required": true, "schema": {"type": "string", "enum": ["ASKU", "NPPA", "DEA", "TO", "UNDEFINED"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PpoBayDTO"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoBayDTO"}}}}}}}, "/api/v1/adjacent-systems/ppo/bay/{bay_type}/unit/{unit_id}": {"put": {"tags": ["ppo-controller"], "operationId": "updateByBayTypeAndIdWithUnitStatus", "parameters": [{"name": "bay_type", "in": "path", "required": true, "schema": {"type": "string", "enum": ["ASKU", "NPPA", "DEA", "TO", "UNDEFINED"]}}, {"name": "unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoUnitDTO"}}}}}}}, "/api/v1/adjacent-systems/ppo/": {"get": {"tags": ["ppo-controller"], "operationId": "getPpoDto", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoDTO"}}}}}}, "put": {"tags": ["ppo-controller"], "operationId": "updateWithDto", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PpoDTO"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoDTO"}}}}}}}, "/users/test": {"post": {"tags": ["nsd-controller"], "operationId": "createUserObj", "parameters": [{"name": "nsdPair", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/NsdPair"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/users/create-api": {"post": {"tags": ["nsd-controller"], "operationId": "createUser", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/tlc/open_keys": {"get": {"tags": ["asku-controller"], "operationId": "getTlcKeys", "parameters": [{"name": "plantMissile", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}, "post": {"tags": ["asku-controller"], "operationId": "setTlcKeys", "parameters": [{"name": "plantMissile", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RocketDto"}}}}}}}, "/api/v1/asku/spl_readiness/{readiness}": {"post": {"tags": ["asku-controller"], "operationId": "setSplReadiness", "parameters": [{"name": "readiness", "in": "path", "required": true, "schema": {"type": "string", "enum": ["БГ № 1", "БГ № 2а", "БГ № 2", "БГ № 3", "ТГ № 4", "Перехід ТГ4 - БГ3", "Перехід БГ3 - ТГ4", "Перехід БГ3 - БГ2а", "Перехід БГ3 - БГ2", "Перехід БГ3 - БГ1", "Перехід БГ2а - БГ1", "Перехід БГ2а - БГ3", "Перехід БГ2а - ТГ4", "Перехід БГ2 - ТГ4", "Перехід БГ2 - БГ3", "Перехід БГ1 - БГ3", "Перехід БГ1 - ТГ4", "Перехід БГ1 - БГ2а", "Відбувається перехід", "Перевірка", "Невідомо"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AskuDto"}}}}}}}, "/api/v1/asku/rocket": {"get": {"tags": ["asku-controller"], "operationId": "getRocket", "parameters": [{"name": "isLeft", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RocketDto"}}}}}}, "post": {"tags": ["asku-controller"], "operationId": "setRocket", "parameters": [{"name": "isLeft", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}, {"name": "encoding", "in": "query", "required": false, "schema": {"type": "string", "default": "UTF-8"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RocketDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AskuDto"}}}}}}}, "/api/v1/asku/rocket/{plant_missile}/reference_points": {"post": {"tags": ["asku-controller"], "operationId": "addReferencePointsByRocket", "parameters": [{"name": "plant_missile", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LaunchReferencePoint"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LaunchReferencePoint"}}}}}}}}, "/api/v1/asku/rocket/{plant_missile}/launch": {"post": {"tags": ["asku-controller"], "operationId": "launchRocket", "parameters": [{"name": "plant_missile", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "isLeft", "in": "query", "required": true, "schema": {"type": "boolean"}}, {"name": "launch_result", "in": "query", "required": true, "schema": {"type": "string", "enum": ["LAUNCH", "FAILURE", "REMOVE", "NONE", "UNKNOWN"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LaunchResultDescription"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AskuDto"}}}}}}}, "/api/v1/asku/rocket/sensors": {"post": {"tags": ["asku-controller"], "operationId": "setTemperature", "parameters": [{"name": "temperature", "in": "query", "required": true, "schema": {"type": "number", "format": "double"}}, {"name": "isLeft", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/plc/tl_keys": {"get": {"tags": ["asku-controller"], "operationId": "loadKeys_1", "parameters": [{"name": "plantMissile", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TlKeysForPlcVersioned"}}}}}}, "post": {"tags": ["asku-controller"], "operationId": "loadKeys", "parameters": [{"name": "plantMissile", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TlKeysForPlcVersioned"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TlKeysForPlcVersioned"}}}}}}}, "/api/v1/asku/plc/message": {"post": {"tags": ["asku-controller"], "operationId": "setPlcMessage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlcMsg"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/plc/launch_timers": {"post": {"tags": ["asku-controller"], "operationId": "handleLaunchTimers", "parameters": [{"name": "action", "in": "query", "required": true, "schema": {"type": "string", "enum": ["START", "UPDATE", "STOP"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartRecord"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/initial_data_with_ts/commit": {"post": {"tags": ["asku-controller"], "operationId": "commitInitialDataWithTimeStamp", "parameters": [{"name": "isLeft", "in": "query", "required": true, "schema": {"type": "boolean"}}, {"name": "initial_data_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LaunchInitialDataDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/initial_data/{plantMissile}": {"post": {"tags": ["asku-controller"], "operationId": "updateLaunchInitialData", "parameters": [{"name": "plantMissile", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LaunchInitialDataDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}, "deprecated": true}}, "/api/v1/asku/fire_order/{plantMissile}": {"post": {"tags": ["asku-controller"], "operationId": "setFireOrder", "parameters": [{"name": "plantMissile", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FireOrderDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/encoding/test": {"get": {"tags": ["asku-controller"], "operationId": "testUtf16Array_1", "parameters": [{"name": "sample", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string", "format": "byte"}}}}}}}, "post": {"tags": ["asku-controller"], "operationId": "testUtf16Array", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "byte"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/adjacent-systems/suto/stats": {"post": {"tags": ["suto-controller"], "operationId": "updateStats", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SutoStatsDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoDto"}}}}}}}, "/api/v1/adjacent-systems/suto/settings": {"get": {"tags": ["suto-controller"], "operationId": "getSutoSettings", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoSettingsDto"}}}}}}, "post": {"tags": ["suto-controller"], "operationId": "updateSettingsWithDto", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SutoSettingsDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoSettingsDto"}}}}}}}, "/api/v1/adjacent-systems/suto/properties/property": {"post": {"tags": ["suto-controller"], "operationId": "updateSutoProperty", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SutoPropertyDto"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoDto"}}}}}}}, "/api/v1/adjacent-systems/suto/outriggers/{outrigger_code_name}": {"post": {"tags": ["suto-controller"], "operationId": "updateOutriggerMalfunctionCode", "parameters": [{"name": "outrigger_code_name", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoDto"}}}}}}}, "/api/v1/adjacent-systems/suto/command/{command_id}/remove": {"post": {"tags": ["suto-controller"], "operationId": "removeCommand", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK"}}}}, "/api/v1/adjacent-systems/suto/command/{command_id}/commit": {"post": {"tags": ["suto-controller"], "operationId": "commitCommand", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK"}}}}, "/api/v1/adjacent-systems/suto/": {"get": {"tags": ["suto-controller"], "operationId": "getSutoDto", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoDto"}}}}}}, "post": {"tags": ["suto-controller"], "operationId": "updateWithDto_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SutoDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoDto"}}}}}}}, "/api/v1/adjacent-systems/sae/command/{command_id}/remove": {"post": {"tags": ["sae-controller"], "operationId": "removeCommand_1", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK"}}}}, "/api/v1/adjacent-systems/sae/command/{command_id}/commit": {"post": {"tags": ["sae-controller"], "operationId": "commitCommand_1", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK"}}}}, "/api/v1/adjacent-systems/ppo/command/{command_id}/remove": {"post": {"tags": ["ppo-controller"], "operationId": "removeCommand_2", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoCommand"}}}}}}}, "/api/v1/adjacent-systems/ppo/command/{command_id}/commit": {"post": {"tags": ["ppo-controller"], "operationId": "commitCommand_2", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoCommand"}}}}}}}, "/api/v1/adjacent-systems/nppa/ncok": {"get": {"tags": ["nppa-controller"], "operationId": "getNcok", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NcokDto"}}}}}}, "post": {"tags": ["nppa-controller"], "operationId": "updateNcok", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NcokDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NppaDto"}}}}}}}, "/api/v1/adjacent-systems/nppa/command/{command_id}/remove": {"post": {"tags": ["nppa-controller"], "operationId": "removeCommand_3", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NppaCommand"}}}}}}}, "/api/v1/adjacent-systems/nppa/command/{command_id}/commit": {"post": {"tags": ["nppa-controller"], "operationId": "commitCommand_3", "parameters": [{"name": "command_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NppaCommand"}}}}}}}, "/api/v1/adjacent-systems/nppa/byn": {"get": {"tags": ["nppa-controller"], "operationId": "<PERSON><PERSON><PERSON>", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BynDto"}}}}}}, "post": {"tags": ["nppa-controller"], "operationId": "updateByn", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BynDto"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NppaDto"}}}}}}}, "/api/v1/asku/state": {"get": {"tags": ["asku-controller"], "operationId": "getAskuReadiness", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AskuStateDto"}}}}}}}, "/api/v1/asku/spl_readiness": {"get": {"tags": ["asku-controller"], "operationId": "getSplReadiness", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "enum": ["БГ № 1", "БГ № 2а", "БГ № 2", "БГ № 3", "ТГ № 4", "Перехід ТГ4 - БГ3", "Перехід БГ3 - ТГ4", "Перехід БГ3 - БГ2а", "Перехід БГ3 - БГ2", "Перехід БГ3 - БГ1", "Перехід БГ2а - БГ1", "Перехід БГ2а - БГ3", "Перехід БГ2а - ТГ4", "Перехід БГ2 - ТГ4", "Перехід БГ2 - БГ3", "Перехід БГ1 - БГ3", "Перехід БГ1 - ТГ4", "Перехід БГ1 - БГ2а", "Відбувається перехід", "Перевірка", "Невідомо"]}}}}}}}, "/api/v1/asku/rocket_form_data": {"get": {"tags": ["asku-controller"], "operationId": "getRocketFormData", "parameters": [{"name": "isLeft", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RocketFormDataDto"}}}}}}}, "/api/v1/asku/reference_points": {"get": {"tags": ["asku-controller"], "operationId": "getLaunchMilestone", "parameters": [{"name": "fire_order_entity_id", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "select_from", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "select_to", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LaunchReferencePoint"}}}}}}}}, "/api/v1/asku/plc/tl_keys/version": {"get": {"tags": ["asku-controller"], "operationId": "getKeysVersion", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/asku/initial_data_with_ts": {"get": {"tags": ["asku-controller"], "operationId": "getInitialDataWithTimeStamp", "parameters": [{"name": "isLeft", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/InitialDataWithTimeStamp"}}}}}}}, "/api/v1/asku/fire_order": {"get": {"tags": ["asku-controller"], "operationId": "getFireOrder", "parameters": [{"name": "isLeft", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/asku/": {"get": {"tags": ["asku-controller"], "operationId": "getAsku", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AskuDto"}}}}}}}, "/api/v1/adjacent-systems/suto/command": {"get": {"tags": ["suto-controller"], "operationId": "getCommand", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SutoCommand"}}}}}}}, "/api/v1/adjacent-systems/sae/status/connection": {"get": {"tags": ["sae-controller"], "operationId": "getConnectionState", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/adjacent-systems/sae/command": {"get": {"tags": ["sae-controller"], "operationId": "getCommand_1", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SaeCommand"}}}}}}}, "/api/v1/adjacent-systems/ppo/status/system": {"get": {"tags": ["ppo-controller"], "operationId": "getStatus", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}}}}}}}, "/api/v1/adjacent-systems/ppo/command": {"get": {"tags": ["ppo-controller"], "operationId": "getCommand_2", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PpoCommand"}}}}}}}, "/api/v1/adjacent-systems/ppo/bay/unit/{unit_state}": {"get": {"tags": ["ppo-controller"], "operationId": "getUnitsWithState", "parameters": [{"name": "unit_state", "in": "path", "required": true, "schema": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PpoUnitDTO"}}}}}}}}, "/api/v1/adjacent-systems/nppa/command": {"get": {"tags": ["nppa-controller"], "operationId": "getCommand_3", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NppaCommand"}}}}}}}, "/api/v1/adjacent-systems/bins/status": {"get": {"tags": ["bins-controller"], "operationId": "getBinsStatus", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}}}}}}}, "/api/v1/adjacent-systems/bins/local_date_time_non_utc": {"get": {"tags": ["bins-controller"], "operationId": "getLocalDateTimeNonUtc", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/v1/adjacent-systems/bins/local_date_time": {"get": {"tags": ["bins-controller"], "operationId": "getLocalDateTimeUtc", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UtcDateTime"}}}}}}}, "/api/v1/adjacent-systems/bins/is_connected": {"get": {"tags": ["bins-controller"], "operationId": "getBinsConnection", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/asku/rocket/{plant_missile}/remove": {"delete": {"tags": ["asku-controller"], "operationId": "removeRocket", "parameters": [{"name": "plant_missile", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "isLeft", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"type": "string"}, {"type": "object", "additionalProperties": {"type": "string"}}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AskuDto"}}}}}}}}, "components": {"schemas": {"SaeDto": {"required": ["adjLock", "adjStart", "adjStop", "adj<PERSON><PERSON><PERSON>", "bsvoltage", "energizedByAdj", "energizedByExternalPowerSource", "energizedByHds", "externalPowerSourceVoltage", "feeder1Status", "feeder2Status", "feeder3Status", "feeder4Status", "feeder5Status", "feeder6Status", "feederNppa1Status", "hdsStatus", "readiness", "saeStatus", "voltageStatus"], "type": "object", "properties": {"saeStatus": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "readiness": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}, "energizedByAdj": {"type": "string", "enum": ["OK", "ERROR", "OFF"]}, "energizedByHds": {"type": "string", "enum": ["OK", "ERROR", "OFF"]}, "energizedByExternalPowerSource": {"type": "string", "enum": ["OK", "ERROR", "OFF"]}, "adjStart": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "adjStop": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "adjLock": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "adjUnlock": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "hdsStatus": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "voltageStatus": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "externalPowerSourceVoltage": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}, "feeder1Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "feeder2Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "feeder3Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "feeder4Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "feeder5Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "feeder6Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "feederNppa1Status": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}, "bsvoltage": {"type": "string", "enum": ["OK", "ERROR", "NOT_ACTIVE"]}}}, "MapEntryStringSaeFeederStatus": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}}, "PpoBayDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "ppoBayType": {"type": "string", "enum": ["ASKU", "NPPA", "DEA", "TO", "UNDEFINED"]}, "firePresent": {"type": "boolean"}, "unitsInPPoBay": {"type": "array", "items": {"$ref": "#/components/schemas/PpoUnitDTO"}}}}, "PpoUnitDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "unitType": {"type": "string", "enum": ["BALLOON", "OPTICAL_SENSOR", "THERMAL_SENSOR", "UNDEFINED"]}, "unitName": {"type": "string"}, "unitAlias": {"type": "string"}, "bayType": {"type": "string", "enum": ["ASKU", "NPPA", "DEA", "TO", "UNDEFINED"]}, "unitStatus": {"type": "string", "enum": ["ON", "OFF", "ERROR", "UNDEFINED"]}}}, "PpoDTO": {"type": "object", "properties": {"status": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}, "ppoBaysList": {"type": "array", "items": {"$ref": "#/components/schemas/PpoBayDTO"}}}}, "NsdPair": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}}}, "LaunchInitialDataDto": {"required": ["altitude", "inclinationAngle", "isProDetected", "latitudeRad", "longitudeRad", "missileOperatingMode", "readiness", "trajectory"], "type": "object", "properties": {"loadTemperature": {"type": "number", "format": "double"}, "latitudeRad": {"type": "string"}, "longitudeRad": {"type": "string"}, "altitude": {"type": "number", "format": "double"}, "inclinationAngle": {"type": "number", "format": "double"}, "trajectory": {"type": "string", "enum": ["BALLISTIC", "AERO_BALLISTIC", "UNKNOWN"]}, "readiness": {"type": "string", "enum": ["БГ № 1", "БГ № 2а", "БГ № 2", "БГ № 3", "ТГ № 4", "Перехід ТГ4 - БГ3", "Перехід БГ3 - ТГ4", "Перехід БГ3 - БГ2а", "Перехід БГ3 - БГ2", "Перехід БГ3 - БГ1", "Перехід БГ2а - БГ1", "Перехід БГ2а - БГ3", "Перехід БГ2а - ТГ4", "Перехід БГ2 - ТГ4", "Перехід БГ2 - БГ3", "Перехід БГ1 - БГ3", "Перехід БГ1 - ТГ4", "Перехід БГ1 - БГ2а", "Відбувається перехід", "Перевірка", "Невідомо"]}, "isProDetected": {"type": "boolean"}, "missileOperatingMode": {"type": "string", "enum": ["BASE_SNS", "UNKNOWN"]}, "validatedByTlc": {"type": "boolean"}, "tlCode": {"type": "string"}, "scheduled": {"type": "boolean"}, "startTime": {"type": "string", "format": "date-time"}}}, "RocketDto": {"required": ["formData", "technicalCondition"], "type": "object", "properties": {"technicalCondition": {"type": "boolean"}, "dateUseM": {"type": "string", "format": "date-time"}, "formData": {"$ref": "#/components/schemas/RocketFormDataDto"}, "initialData": {"$ref": "#/components/schemas/LaunchInitialDataDto"}, "initialDataTS": {"type": "string", "format": "date-time"}, "initialDataSource": {"type": "string", "enum": ["MSG", "VOICE", "PAPER_WORK", "PLC", "OTHER", "UNKNOWN"]}, "initialDataSourceDescription": {"type": "string"}, "storedTlKeys": {"type": "array", "items": {"type": "string"}}, "sensorTemperature": {"type": "number", "format": "double"}}}, "RocketFormDataDto": {"required": ["alpType", "gsnType", "isTelemetryIntegrated", "plantMissile", "purposeType", "warhead"], "type": "object", "properties": {"plantMissile": {"type": "string"}, "warhead": {"type": "string", "enum": ["MFBCH", "CBCH", "UNDEFINED"]}, "gsnType": {"type": "string", "enum": ["NO_GSN", "K_GSN", "OE_GSN", "UNDEFINED"]}, "alpType": {"type": "string", "enum": ["NO_ALP", "FOUR_ALP", "TWO_ALP", "UNDEFINED"]}, "isTelemetryIntegrated": {"type": "boolean"}, "purposeType": {"type": "string", "enum": ["COMBAT", "TEM", "UNDEFINED"]}}}, "AskuDto": {"type": "object", "properties": {"plateNumber": {"type": "string"}, "unitTitle": {"type": "string"}, "startedDate": {"type": "string", "format": "date-time"}, "splReadiness": {"type": "string", "enum": ["БГ № 1", "БГ № 2а", "БГ № 2", "БГ № 3", "ТГ № 4", "Перехід ТГ4 - БГ3", "Перехід БГ3 - ТГ4", "Перехід БГ3 - БГ2а", "Перехід БГ3 - БГ2", "Перехід БГ3 - БГ1", "Перехід БГ2а - БГ1", "Перехід БГ2а - БГ3", "Перехід БГ2а - ТГ4", "Перехід БГ2 - ТГ4", "Перехід БГ2 - БГ3", "Перехід БГ1 - БГ3", "Перехід БГ1 - ТГ4", "Перехід БГ1 - БГ2а", "Відбувається перехід", "Перевірка", "Невідомо"]}, "tpcLeft": {"$ref": "#/components/schemas/Tpc"}, "tpcRight": {"$ref": "#/components/schemas/Tpc"}, "leftRocket": {"$ref": "#/components/schemas/RocketDto"}, "rightRocket": {"$ref": "#/components/schemas/RocketDto"}, "position": {"$ref": "#/components/schemas/Position"}}}, "Position": {"type": "object", "properties": {"latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "altitude": {"type": "number", "format": "double"}, "datum": {"type": "string", "enum": ["WGS84", "NAD83", "NAD27"]}}}, "Tpc": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "string", "format": "date-time"}, "tpcLoadState": {"type": "string", "enum": ["NONE", "TPC_ONLY", "TPC_WITH_ROCKET", "ERROR", "UNKNOWN"]}, "adjacentSystemType": {"type": "string", "enum": ["BINS", "MSU", "NPPA", "NCOK", "BYN", "SAE", "SUTO", "PPO", "PLC", "ASKU", "TPC", "UNCLASSIFIED", "UNDEFINED", "NSD", "CCV"]}, "errorMessage": {"type": "string"}, "malfunctionPresent": {"type": "boolean"}, "status": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}}}, "LaunchReferencePoint": {"type": "object", "properties": {"referencePoint": {"type": "string"}, "timeStamp": {"type": "string", "format": "date-time"}, "plantMissile": {"type": "string"}}}, "LaunchResultDescription": {"type": "object", "properties": {"code": {"type": "string"}, "description": {"type": "string"}}}, "TlKeysForPlcVersioned": {"type": "object", "properties": {"version": {"type": "integer", "format": "int64"}, "keys": {"type": "array", "items": {"type": "string"}}}}, "PlcMsg": {"required": ["msgType", "payload"], "type": "object", "properties": {"msgType": {"type": "string", "enum": ["COMMIT", "INFO", "WARNING", "ERROR"]}, "systemType": {"type": "string", "enum": ["BINS", "MSU", "NPPA", "NCOK", "BYN", "SAE", "SUTO", "PPO", "PLC", "ASKU", "TPC", "UNCLASSIFIED", "UNDEFINED", "NSD", "CCV"]}, "alias": {"type": "string"}, "payload": {"type": "string"}}}, "StartRecord": {"type": "object", "properties": {"launchType": {"type": "string", "enum": ["ОТР1", "ОТР2", "ОТР1 та ОТР2"]}}}, "FireOrderDto": {"type": "object", "properties": {"launchInitialData": {"$ref": "#/components/schemas/LaunchInitialDataDto"}, "orderInfo": {"$ref": "#/components/schemas/OrderInfoDto"}}}, "OrderInfoDto": {"required": ["entityId", "validUntil"], "type": "object", "properties": {"entityId": {"type": "string", "format": "uuid"}, "validUntil": {"type": "string", "format": "date-time"}}}, "SutoStatsDto": {"required": ["armLiftStrokePosition", "levelingCyclesCount", "mainPumpRPM", "overallArmLiftingsCount", "overallOperatingTime", "pitch", "pressureInImpulseSection", "roll", "temperatureRR", "workingFluidLevel"], "type": "object", "properties": {"roll": {"type": "integer", "format": "int32"}, "pitch": {"type": "integer", "format": "int32"}, "armLiftStrokePosition": {"type": "integer", "format": "int32"}, "levelingCyclesCount": {"type": "integer", "format": "int32"}, "pressureInImpulseSection": {"type": "number", "format": "double"}, "temperatureRR": {"type": "integer", "format": "int32"}, "workingFluidLevel": {"type": "integer", "format": "int32"}, "mainPumpRPM": {"type": "integer", "format": "int32"}, "overallOperatingTime": {"type": "integer", "format": "int32"}, "overallArmLiftingsCount": {"type": "integer", "format": "int32"}}}, "SutoDto": {"required": ["id", "properties", "stats"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}, "stats": {"$ref": "#/components/schemas/SutoStatsDto"}, "leftFrontOutriggerEmergencyCode": {"type": "integer", "format": "int32"}, "rightFrontOutriggerEmergencyCode": {"type": "integer", "format": "int32"}, "leftRearOutriggerEmergencyCode": {"type": "integer", "format": "int32"}, "rightRearOutriggerEmergencyCode": {"type": "integer", "format": "int32"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/SutoPropertyDto"}}}}, "SutoPropertyDto": {"required": ["id", "name", "propertyType", "state"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "state": {"type": "string", "enum": ["ON", "OFF", "CANCELLED"]}, "propertyType": {"type": "string", "enum": ["REGULAR", "MALFUNCTION"]}}}, "SutoSettingsDto": {"required": ["chassisState"], "type": "object", "properties": {"chassisState": {"type": "string", "enum": ["MARCH", "PARKING", "UNKNOWN"]}}}, "PpoCommand": {"required": ["adjacentSystem", "caption", "command"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "command": {"type": "string"}, "caption": {"type": "string"}, "adjacentSystem": {"type": "string", "enum": ["BINS", "MSU", "NPPA", "NCOK", "BYN", "SAE", "SUTO", "PPO", "PLC", "ASKU", "TPC", "UNCLASSIFIED", "UNDEFINED", "NSD", "CCV"]}, "generationTime": {"type": "string", "format": "date-time"}, "executionTime": {"type": "string", "format": "date-time"}}}, "NcokDto": {"type": "object", "properties": {"systemStatus": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}, "operatingMode": {"type": "string", "enum": ["COMBAT", "TESTING", "NOT_SELECTED"]}, "tvNcok": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "isNcokConnected": {"type": "boolean"}, "isSutoConnected": {"type": "boolean"}, "nppaTestResult": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "appPresence": {"type": "boolean"}, "otr1AppPresence": {"type": "boolean"}, "otr2AppPresence": {"type": "boolean"}, "otr1TestResult": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr2TestResult": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "isOtr1Lunched": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "isOtr2Lunched": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr1BinsInitialSetup": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr2BinsInitialSetup": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otrBinsPreciseSetup": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr1tvNoIns": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr2tvNoIns": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr1BasSnsPz": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "otr2BasSnsPz": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}}}, "BynDto": {"type": "object", "properties": {"systemStatus": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}, "operatingMode": {"type": "string", "enum": ["COMBAT", "TESTING", "NOT_SELECTED"]}, "tvByn": {"type": "string", "enum": ["OK", "WARNING", "ERROR", "NOT_CONNECTED", "UNDEFINED"]}, "isConnected": {"type": "boolean"}, "isNcok": {"type": "boolean"}, "isRgOutNcok": {"type": "boolean"}, "isBuveF2": {"type": "boolean"}, "isBuveF4": {"type": "boolean"}, "isBasuOtr1F3": {"type": "boolean"}, "isBasuOtr2F3": {"type": "boolean"}, "isF1": {"type": "boolean"}, "isF2": {"type": "boolean"}, "isF3": {"type": "boolean"}, "isF4": {"type": "boolean"}, "isF5": {"type": "boolean"}, "isNppaConnected": {"type": "boolean"}, "isBasuOtr1Connected": {"type": "boolean"}, "isBasuOtr2Connected": {"type": "boolean"}}}, "NppaDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["Норма", "Увага", "Помилка", "Немає зв'язку", "Невідомо", "Будь-який", "РЧ дані"]}, "ncok": {"$ref": "#/components/schemas/NcokDto"}, "byn": {"$ref": "#/components/schemas/BynDto"}, "leftRocketFormData": {"$ref": "#/components/schemas/RocketFormDataDto"}, "rightRocketFormData": {"$ref": "#/components/schemas/RocketFormDataDto"}, "leftRocketInitialData": {"$ref": "#/components/schemas/LaunchInitialDataDto"}, "rightRocketInitialData": {"$ref": "#/components/schemas/LaunchInitialDataDto"}}}, "NppaCommand": {"required": ["adjacentSystem", "caption", "command"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "command": {"type": "string"}, "caption": {"type": "string"}, "adjacentSystem": {"type": "string", "enum": ["BINS", "MSU", "NPPA", "NCOK", "BYN", "SAE", "SUTO", "PPO", "PLC", "ASKU", "TPC", "UNCLASSIFIED", "UNDEFINED", "NSD", "CCV"]}, "generationTime": {"type": "string", "format": "date-time"}, "executionTime": {"type": "string", "format": "date-time"}}}, "AskuStateDto": {"type": "object", "properties": {"readiness": {"type": "string", "enum": ["БГ № 1", "БГ № 2а", "БГ № 2", "БГ № 3", "ТГ № 4", "Перехід ТГ4 - БГ3", "Перехід БГ3 - ТГ4", "Перехід БГ3 - БГ2а", "Перехід БГ3 - БГ2", "Перехід БГ3 - БГ1", "Перехід БГ2а - БГ1", "Перехід БГ2а - БГ3", "Перехід БГ2а - ТГ4", "Перехід БГ2 - ТГ4", "Перехід БГ2 - БГ3", "Перехід БГ1 - БГ3", "Перехід БГ1 - ТГ4", "Перехід БГ1 - БГ2а", "Відбувається перехід", "Перевірка", "Невідомо"]}, "leftRocketCondition": {"type": "boolean"}, "rightRocketCondition": {"type": "boolean"}}}, "InitialDataWithTimeStamp": {"type": "object", "properties": {"initialDataId": {"type": "integer", "format": "int64"}, "launchInitialDataDto": {"$ref": "#/components/schemas/LaunchInitialDataDto"}, "timeStamp": {"type": "string", "format": "date-time"}}}, "SutoCommand": {"required": ["adjacentSystem", "caption", "command"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "command": {"type": "string"}, "caption": {"type": "string"}, "adjacentSystem": {"type": "string", "enum": ["BINS", "MSU", "NPPA", "NCOK", "BYN", "SAE", "SUTO", "PPO", "PLC", "ASKU", "TPC", "UNCLASSIFIED", "UNDEFINED", "NSD", "CCV"]}, "generationTime": {"type": "string", "format": "date-time"}, "executionTime": {"type": "string", "format": "date-time"}}}, "SaeCommand": {"required": ["adjacentSystem", "caption", "command"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "command": {"type": "string"}, "caption": {"type": "string"}, "adjacentSystem": {"type": "string", "enum": ["BINS", "MSU", "NPPA", "NCOK", "BYN", "SAE", "SUTO", "PPO", "PLC", "ASKU", "TPC", "UNCLASSIFIED", "UNDEFINED", "NSD", "CCV"]}, "generationTime": {"type": "string", "format": "date-time"}, "executionTime": {"type": "string", "format": "date-time"}}}, "LocalTime": {"type": "object", "properties": {"hour": {"type": "integer", "format": "int32"}, "minute": {"type": "integer", "format": "int32"}, "second": {"type": "integer", "format": "int32"}, "nano": {"type": "integer", "format": "int32"}}}, "UtcDateTime": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "time": {"$ref": "#/components/schemas/LocalTime"}}}}}}