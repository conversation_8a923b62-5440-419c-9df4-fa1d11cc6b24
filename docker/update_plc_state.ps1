$url = "http://localhost:8079/api/v1/adjacent-systems/ppo/"
$headers = @{
    "Token" = "plc12345"
}
$timeoutMinutes = 10
$startTime = Get-Date
$isFirst200 = $false

Start-Sleep -Milliseconds 1000

while ($true) {
    try {
        $response = Invoke-WebRequest -Uri $url -Method Get -Headers $headers -ErrorAction Stop

        if ($response.StatusCode -eq 200) {
            $isFirst200 = $true
            $startTime = Get-Date  # Reset timer on connection
            # Place your code for successful connection here
        } elseif ($response.StatusCode -eq 404) {
            Write-Host "host unavailable"
            break
        }
    } catch {
        Write-Host "request error: $_"
        Write-Host "host unavailable"
        # Do not break immediately; continue and check timer
    }

    # Check if time without connection exceeds timeout
    $elapsed = (Get-Date) - $startTime
    if ($elapsed.TotalMinutes -ge $timeoutMinutes) {
        Write-Host "No connection for 10 minutes, exiting loop."
        break
    }

    Start-Sleep -Milliseconds 100
}
