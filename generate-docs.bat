@echo off
title Maven Build and Cleanup

echo =========================================
echo  1. Running mvn integration-test -Pgenerate-openapi -DskipTests ...
echo =========================================
START "" mvn integration-test -Pgenerate-openapi -DskipTests

REM Wait up to 60 seconds for the openapi.yml file to appear
setlocal ENABLEDELAYEDEXPANSION
set "counter=0"
:waitForFile
if exist "target\generated-sources\openapi\openapi.yml" (
    echo OpenAPI file found. Proceeding to the next step.
    goto :continueScript
)
if !counter! geq 180 (
    echo Timeout waiting for openapi.yml file.
    goto :eof
)
echo Waiting...
timeout /t 1 /nobreak >nul
set /a counter+=1
goto :waitForFile

:continueScript
echo.
echo =========================================
echo  3. Terminating all Java processes...
echo =========================================
START "" taskkill /F /IM java.exe

echo.
echo =========================================
echo  Script finished successfully.
echo =========================================

pause
