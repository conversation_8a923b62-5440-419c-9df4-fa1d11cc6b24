@echo off
echo Fetching OpenAPI YAML from running application...
echo.

echo Checking if application is running on http://localhost:8079...
curl -s -o nul -w "%%{http_code}" http://localhost:8079/api-docs > temp_status.txt
set /p status=<temp_status.txt
del temp_status.txt

if "%status%"=="200" (
    echo ✓ Application is running!
    echo Fetching OpenAPI JSON and converting to YAML...
    
    REM Create directory if it doesn't exist
    if not exist "target\generated-sources\openapi" mkdir "target\generated-sources\openapi"
    
    REM Fetch JSON and save
    curl -s http://localhost:8079/api-docs > target\generated-sources\openapi\openapi.json
    
    REM Note: This saves as JSON. For YAML conversion, you'd need a tool like yq
    echo ✓ OpenAPI JSON saved to: target\generated-sources\openapi\openapi.json
    echo.
    echo To convert to YAML, you can:
    echo 1. Use an online converter like https://www.json2yaml.com/
    echo 2. Install yq tool: https://github.com/mikefarah/yq
    echo 3. Use the Maven plugin with the generate-openapi.bat script
    
    REM Copy to root as well
    copy "target\generated-sources\openapi\openapi.json" "openapi.json" >nul
    echo ✓ Also copied to: openapi.json
) else (
    echo ✗ Application is not running or not accessible.
    echo Please start your application first with: mvn spring-boot:run
    echo Then run this script again.
)

echo.
pause
