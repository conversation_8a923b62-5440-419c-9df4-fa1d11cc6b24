package com.deb.spl.control.service;

import com.deb.spl.control.controller.BadRequestException;
import com.deb.spl.control.controller.NotFoundException;
import com.deb.spl.control.data.AdjacentSystemProperty;
import com.deb.spl.control.data.AdjacentSystemStatus;
import com.deb.spl.control.data.AdjacentSystemType;
import com.deb.spl.control.data.asku.TpcState;
import com.deb.spl.control.data.nppa.CommandValidationResult;
import com.deb.spl.control.data.suto.*;
import com.deb.spl.control.repository.suto.SutoCommandRepository;
import com.deb.spl.control.repository.suto.SutoHistoryRepository;
import com.deb.spl.control.repository.suto.SutoPropertyRepository;
import com.deb.spl.control.repository.suto.SutoRepository;
import com.deb.spl.control.service.asku.AskuService;
import com.deb.spl.control.views.events.AdjacentSystemUpdateEventType;
import com.deb.spl.control.views.events.SutoEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class SutoService extends ServiceWithCommandCommitment<SutoCommand> {
    private final SutoHistoryRepository sutoHistoryRepository;
    private final SutoPropertyMapper sutoPropertyMapper;
    private final SutoMapper sutoMapper;
    private final SutoStatsToDtoMapper toStatsMapper;
    private final SutoCommandToDaoMapper commandToDaoMapper;

    private final SutoRepository sutoRepository;
    private final SutoPropertyRepository sutoPropertyRepository;
    private final SutoCommandRepository sutoCommandRepository;
    private final SutoSettingsToDtoMapper sutoSettingsToDtoMapper;
    private final AskuService askuService;

    private Suto sutoEntity;

    public SutoService(SutoMapper sutoMapper,
                       SutoStatsToDtoMapper toStatsMapper,
                       SutoCommandToDaoMapper commandToDaoMapper,
                       SutoRepository sutoRepository,
                       SutoPropertyRepository sutoPropertyRepository,
                       SutoPropertyMapper sutoPropertyMapper,
                       SutoCommandRepository sutoCommandRepository,
                       SutoHistoryRepository sutoHistoryRepository,
                       SutoSettingsToDtoMapper sutoSettingsToDtoMapper,
                       AskuService askuService,
                       @Value("${suto.command.command-validity-time-sec}") int commandValidityTime) {
        super(sutoHistoryRepository, commandValidityTime, askuService);
        this.sutoMapper = sutoMapper;
        this.toStatsMapper = toStatsMapper;
        this.commandToDaoMapper = commandToDaoMapper;
        this.sutoRepository = sutoRepository;
        this.sutoPropertyRepository = sutoPropertyRepository;
        this.sutoPropertyMapper = sutoPropertyMapper;
        this.sutoCommandRepository = sutoCommandRepository;
        this.sutoSettingsToDtoMapper = sutoSettingsToDtoMapper;
        this.askuService = askuService;
        this.sutoHistoryRepository = sutoHistoryRepository;

        loadInitialData();
        initializeEvents();
    }

    private void loadInitialData() {
        Optional<SutoDao> dao = sutoRepository.load();

        if (dao.isEmpty()) {
            log.error("Cant load Suto. Check if DB is corrupted or valid Suto entity exists ");
            throw new RuntimeException("Cant load Suto. Check if DB is corrupted or valid Suto entity exists ");
        }

        this.sutoEntity = sutoMapper.map(dao.get());
    }

    public Optional<SutoDto> getSutoDto() {
        if (this.sutoEntity == null) {
            log.error("suto wasn't initialized");
            throw new NotFoundException("suto wasn't initialized");
        }

        return Optional.of(sutoMapper.toDto(this.sutoEntity));
    }

    public AdjacentSystemStatus getStatus() {
        return sutoEntity.getStatus() == null ? AdjacentSystemStatus.UNDEFINED : sutoEntity.getStatus();
    }

    @Transactional
    public SutoDto updateWithStats(@NotNull SutoStatsDto dto) {
        SutoStats stats = toStatsMapper.map(dto);
        if (stats.equals(this.sutoEntity.getStats())) {
            return sutoMapper.toDto(this.sutoEntity);
        }

        Suto sutoUnderUpdate = Suto.copy(this.sutoEntity);
        sutoUnderUpdate.setStats(stats);
        updateSutoStatus(sutoUnderUpdate);

        return sutoMapper.toDto(this.sutoEntity);
    }

    @Transactional
    public SutoDto updateWithOutriggerMalfunctionCodes(@NotNull @NotBlank String outriggerCodeName, @NotNull @Min(0) int intCode) {
        Suto sutoUnderUpdate = null;
        OutriggerEmergencyCode code = OutriggerEmergencyCode.decodeFromInt(intCode);

        switch (outriggerCodeName) {
            case ("leftFrontOutriggerEmergencyCode") -> {
                if (!this.sutoEntity.getLeftFrontOutriggerEmergencyCode().equals(code)) {
                    sutoUnderUpdate = Suto.copy(this.sutoEntity);
                    sutoUnderUpdate.setLeftFrontOutriggerEmergencyCode(code);
                }
            }
            case ("rightFrontOutriggerEmergencyCode") -> {
                if (!this.sutoEntity.getRightFrontOutriggerEmergencyCode().equals(code)) {
                    sutoUnderUpdate = Suto.copy(this.sutoEntity);
                    sutoUnderUpdate.setRightFrontOutriggerEmergencyCode(code);
                }
            }
            case ("leftRearOutriggerEmergencyCode") -> {
                if (!this.sutoEntity.getLeftRearOutriggerEmergencyCode().equals(code)) {
                    sutoUnderUpdate = Suto.copy(this.sutoEntity);
                    sutoUnderUpdate.setLeftRearOutriggerEmergencyCode(code);
                }
            }
            case ("rightRearOutriggerEmergencyCode") -> {
                if (!this.sutoEntity.getRightRearOutriggerEmergencyCode().equals(code)) {
                    sutoUnderUpdate = Suto.copy(this.sutoEntity);
                    sutoUnderUpdate.setRightRearOutriggerEmergencyCode(code);
                }
            }
            default -> throw new BadRequestException("invalid outrigger name " + outriggerCodeName);
        }

        if (sutoUnderUpdate != null) {
            if (sutoUnderUpdate.getStatus() != AdjacentSystemStatus.ERROR && code != OutriggerEmergencyCode.NONE) {
                sutoUnderUpdate.setStatus(AdjacentSystemStatus.ERROR);
            }
            updateSutoStatus(sutoUnderUpdate);
        }

        return sutoMapper.toDto(this.sutoEntity);
    }

    @Transactional
    public SutoDto updateSutoProperty(@NotNull @Valid List<SutoPropertyDto> dtoList) {
        Map<String, SutoProperty> propertyMap = dtoList
                .stream()
                .map(sutoPropertyMapper::map)
                .collect(Collectors.toMap(p -> p.getName(), Function.identity()));

        propertyMap = loadPropertiesCaptions(propertyMap);

        Suto sutoUnderUpdate = Suto.copy(this.sutoEntity);

        for (SutoProperty property : propertyMap.values()) {
            if (property == null) {
                log.error("property name doesn't match an id " + property);
                throw new BadRequestException("property name doesn't match an id " + property);
            }

            if ((sutoUnderUpdate.getProperties().get(property.getName()) == null)) {
                log.error("property can't be found " + property);
                throw new NotFoundException("property can't be found " + property);
            }

            sutoUnderUpdate.getProperties().get(property.getName()).setState(property.getState());
        }

        updateSutoStatus(sutoUnderUpdate);

        return sutoMapper.toDto(this.sutoEntity);
    }

    @Transactional
    public SutoDto updateWithDto(@NotNull @Valid SutoDto dto) {
        Suto converted = sutoMapper.map(dto);
        converted.setProperties(loadPropertiesCaptions(converted.getProperties()));

        if (!this.sutoEntity.equals(converted)) {
            updateSutoStatus(converted);
        }

        return sutoMapper.toDto(this.sutoEntity);
    }

    public Map<String, SutoProperty> loadPropertiesCaptions(@NotNull Map<String, SutoProperty> properties) {
        List<Long> ids = properties.values().stream().map(AdjacentSystemProperty::getId).toList();
        List<SutoProperty> loadedProperties = new ArrayList<>(sutoPropertyRepository.findAllByIdIn(ids));

        Map<String, SutoProperty> result = new HashMap<>(properties);
        for (SutoProperty property : result.values()) {
            Iterator<SutoProperty> iterator = loadedProperties.iterator();
            while (iterator.hasNext()) {
                SutoProperty found = iterator.next();
                if (found.getId().equals(property.getId())) {
                    if (!found.getName().equalsIgnoreCase(property.getName())) {
                        throw new BadRequestException("the name doesn't match an id");
                    }
                    property.setCaption(found.getCaption());
                    iterator.remove();
                    break;
                }
            }
        }
        return result;
    }

    @Transactional
    public void updateSutoStatus(@NotNull Suto sutoUnderProcessing) {
        if (sutoUnderProcessing.getStatus() == null) {
            log.warn("Received SAE without status " + sutoUnderProcessing + " .Setting status automatically to UNDEFINED");
            sutoUnderProcessing.setStatus(AdjacentSystemStatus.UNDEFINED);
        }

        //check if ids equals to entities
        if (!validateSuto(sutoUnderProcessing)) {
            throw new BadRequestException();
        }

        if (sutoUnderProcessing.equals(sutoEntity)) {
            return;
        }

        if (sutoUnderProcessing.getSutoSettings() == null) {
            sutoUnderProcessing.setSutoSettings(sutoEntity.getSutoSettings() != null ? sutoEntity.getSutoSettings() :
                    SutoSettings.builder().chassisState(ChassisState.UNKNOWN).build());
        }
        SutoDao dao = sutoMapper.toDao(sutoUnderProcessing);
        dao.setId(null);
        dao = sutoRepository.save(dao);
        this.sutoEntity = sutoMapper.map(dao);

        updateSutoEvent(sutoUnderProcessing);
    }

    public boolean validateSuto(@NotNull Suto sutoUnderProcessing) {
        try (ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory()) {
            Validator validator = validatorFactory.getValidator();

            Set<ConstraintViolation<Suto>> sutoViolations = validator.validate(sutoUnderProcessing);
            if (!sutoViolations.isEmpty()) {
                log.error(Arrays.toString(sutoViolations.toArray()));
                throw new BadRequestException(Arrays.toString(sutoViolations.toArray()));
            }

            StringBuilder builder = new StringBuilder();
            for (SutoProperty property : sutoUnderProcessing.getProperties().values()) {
                Set<ConstraintViolation<SutoProperty>> violations = validator.validate(property);
                if (!violations.isEmpty()) {
                    if (builder.isEmpty()) {
                        builder.append("\n");
                    }

                    builder.append("SutoProperty ").append(property.toString()).append("violations: ").append(Arrays.toString(violations.toArray()));
                }
            }

            Map<SutoProperty, String> propertiesViolations = validateSutoPropertyAgainstSaved(sutoUnderProcessing.getProperties().values());
            if (!propertiesViolations.isEmpty()) {
                StringBuilder errorStringBuilder = new StringBuilder();
                errorStringBuilder.append("can't update SUTO because of SutoProperty violations : ");

                for (Map.Entry<SutoProperty, String> violations : propertiesViolations.entrySet()) {
                    errorStringBuilder.append("\n{ entry: ").append(violations.getKey()).append(" violations: ").append(violations.getValue()).append("}");
                }

                log.error(errorStringBuilder.toString());
                throw new BadRequestException(errorStringBuilder.toString());
            }

            if (!builder.isEmpty()) {
                log.error(builder.toString());
                throw new BadRequestException(builder.toString());
            }

        } catch (ValidationException e) {
            log.error(e.getMessage() + " \t" + Arrays.toString(e.getStackTrace()));
            throw new BadRequestException(e.getMessage() + " \t" + Arrays.toString(e.getStackTrace()));
        }

        return true;
    }

    public Map<SutoProperty, String> validateSutoPropertyAgainstSaved(@NotNull @Size(min = 1) Collection<SutoProperty> sutoProperties) {
        List<Long> ids = sutoProperties.stream().map(AdjacentSystemProperty::getId).toList();
        List<SutoProperty> foundProperties = sutoPropertyRepository.findAllByIdIn(ids);

        if (foundProperties == null) {
            log.error("No properties exists for provided request " + Arrays.toString(sutoProperties.toArray()));
            throw new BadRequestException("No properties exists for provided request " + Arrays.toString(sutoProperties.toArray()));
        }

        Map<SutoProperty, String> persistedPropertiesViolations = new HashMap<>();
        for (SutoProperty property : sutoProperties) {
            Optional<SutoProperty> result = foundProperties.stream().filter(s -> s.getId().equals(property.getId())).findFirst();
            if (result.isEmpty()) {
                persistedPropertiesViolations.put(property, "id doesn't exist");
            } else {
                if (!result.get().getName().equalsIgnoreCase(property.getName())) {
                    persistedPropertiesViolations.put(property, "a name doesn't match the id");
                }
            }
        }

        return persistedPropertiesViolations;
    }

    private SutoEvent currentSutoEvent;

    private SutoEvent previousSutoEvent;

    private void initializeEvents() {
        this.currentSutoEvent = SutoEvent.builder().suto(this.sutoEntity).eventType(AdjacentSystemUpdateEventType.UNDEFINED).source(this).build();
    }

    private void updateSutoEvent(Suto sutoUnderProcessing) {
        if (currentSutoEvent != null) {
            previousSutoEvent = currentSutoEvent;
        }
        try {
            AdjacentSystemUpdateEventType eventType;
            if (sutoUnderProcessing.isMalfunctionPresent()) {
                eventType = (AdjacentSystemUpdateEventType.ERROR_OCCURRED);
            } else {
                switch (sutoUnderProcessing.getStatus()) {
                    case OK -> eventType = AdjacentSystemUpdateEventType.DATA_UPDATE;
                    case NOT_CONNECTED -> eventType = AdjacentSystemUpdateEventType.NOT_CONNECTED;
                    default -> eventType = AdjacentSystemUpdateEventType.UNDEFINED;
                }
            }
            this.currentSutoEvent = SutoEvent.builder().eventType(eventType).suto(sutoUnderProcessing).source(currentSutoEvent.getSource()).build();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    void fireUpdateEvent() {
        if (currentSutoEvent == null) {
            log.info("no SUTO event for update");
            return;
        }

        if (currentSutoEvent.equals(previousSutoEvent)) {
            log.debug("SUTO event is the same as the previous");
            return;
        }
        Broadcaster.broadcast(currentSutoEvent);
    }

    @Value("${suto.command.command-validity-time-sec}")
    private int commandValidityTime;
    public void setCommand(@NotNull @Valid SutoCommand command) {
        if (!command.getAdjacentSystem().equals(AdjacentSystemType.SUTO)) {
            log.error("received unexpected command " + command + " in the" + this.getClass().getSimpleName());
            return;
        }

        if (this.topMostCommand != null && this.topMostCommand.equals(command)) {
            if (!LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTime))) {
                return;
            }
        }

        CommandValidationResult validationResult = validateCommandOnTimeDelay(command);
        if (!validationResult.isValid()) {
            try {
                showValidationErrorMsg(validationResult.errorMessageUa(), AdjacentSystemType.SUTO, this.getClass());
            } catch (Exception e) {
                log.error(e.getMessage() + " " + Arrays.toString(e.getStackTrace()));
            }
            return;
        }

        this.commandGenerationTime = command.getGenerationTime();
        this.topMostCommand = command;
        sutoHistoryRepository.save(command);

        log.info("added ppo command " + command + " at" + commandGenerationTime.toString());
    }

    public Optional<SutoCommand> getCommand() {
        if (this.topMostCommand == null && commandGenerationTime == null) {
            String message = "none active suto command was found";
            log.debug(message);
            throw new NotFoundException();
        }

        if (LocalDateTime.now().isAfter(commandGenerationTime.plusSeconds(commandValidityTime))) {
            return Optional.empty();
        }
        Optional<SutoCommand> validCommand = Optional.ofNullable(this.topMostCommand);
        if (validCommand.isEmpty()) {
            log.debug("cant find any command for SUTO");
            throw new NotFoundException("cant find any command for SUTO");
        }

        return validCommand;
    }

    public List<SutoCommand> getAvailableCommands() {
        return sutoCommandRepository.getAllCommands().stream().map(commandToDaoMapper::map).toList();
    }

    public List<SutoProperty> getAvailableProperties() {
        return this.sutoEntity.getProperties().values().stream().sorted(Comparator.comparing(AdjacentSystemProperty::getId)).toList();
    }

    public Stream<SutoProperty> getPropertiesStream(int page, int pageSize) {

        return this.sutoEntity.getProperties().values().stream()
                .sorted(Comparator.comparing(AdjacentSystemProperty::getId))
                .skip((long) (page) * pageSize).limit(pageSize)
                .limit(this.sutoEntity.getProperties().values().size());
    }

    public Optional<SutoProperty> getSutoPropertyByName(String propertyName) {

        return Optional.ofNullable(sutoEntity.getProperties().get(propertyName));
    }

    public Optional<SutoSettings> getSutoSettings() {
        if (sutoEntity == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(sutoEntity.getSutoSettings());
    }

    @Transactional
    public void updateSutoSettings(@NotNull TpcState lefTpcState, @NotNull TpcState rightTpcState, @NotNull ChassisState chassisState) {

        if (sutoEntity.getSutoSettings() != null && chassisState != sutoEntity.getSutoSettings().getChassisState()) {

            log.info("changed chassis state by operator");
            updateSutoSettings(chassisState);
        }

        if (askuService.getLeftTpcLoadState().isPresent() && lefTpcState != askuService.getLeftTpcLoadState().get()) {

            askuService.setLeftTpcStateWithAsku(lefTpcState);
            log.info("changed left tpc state by operator");
        }

        if (askuService.getRightTpcLoadState().isPresent() && rightTpcState != askuService.getRightTpcLoadState().get()) {

            askuService.setRightTpcStateWithAsku(rightTpcState);
            log.info("changed right tpc state by operator");
        }
    }

    @Transactional
    public void updateSutoSettings(@NotNull ChassisState chassisState) {
        Suto underProcessing = Suto.copy(this.sutoEntity);
        underProcessing.setSutoSettings(new SutoSettings(chassisState));
        updateSutoStatus(underProcessing);

        log.info("set SUTO chassis state for by PLC " + chassisState.getValueUa());
    }

    public Optional<SutoSettingsDto> getSutoSettingsDto() {
        if (sutoEntity.getSutoSettings() == null) {
            String msg = "SutoSettings is null " + this;
            log.error(msg);
            throw new NotFoundException(msg);
        }
        return Optional.of(sutoSettingsToDtoMapper.toDto(sutoEntity.getSutoSettings()));
    }

    @Transactional
    public void updateSutoSettingsWithDto(@NotNull @Valid SutoSettingsDto dto) {
        updateSutoSettings(dto.chassisState());
    }
}
