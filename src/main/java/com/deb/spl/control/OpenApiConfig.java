package com.deb.spl.control;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(
    info = @Info(
        title = "Vehicle Control Services API",
        version = "1.4.7",
        description = "This API provides services for controlling and monitoring vehicles."
    )
)
public class OpenApiConfig {
    // This class can be empty. Its only purpose is to hold the @OpenAPIDefinition.
}