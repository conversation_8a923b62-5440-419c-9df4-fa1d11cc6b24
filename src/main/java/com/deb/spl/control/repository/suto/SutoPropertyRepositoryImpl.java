package com.deb.spl.control.repository.suto;

import com.deb.spl.control.data.suto.SutoPropertyMapper;
import com.deb.spl.control.data.suto.SutoProperty;
import com.deb.spl.control.data.suto.SutoPropertyDao;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

@Repository
public class SutoPropertyRepositoryImpl implements SutoPropertyRepository {
    private final EntityManager entityManager;

    private final SutoPropertyMapper propertyToDaoMapper;

    public SutoPropertyRepositoryImpl(EntityManager entityManager, SutoPropertyMapper propertyToDaoMapper) {
        this.entityManager = entityManager;
        this.propertyToDaoMapper = propertyToDaoMapper;
    }

    @Override
    public Optional<SutoProperty> findById(Long id) {
        return Optional.ofNullable(entityManager.find(SutoProperty.class, id));
    }

    @Override
    public List<SutoProperty> findAllByIdIn(@NotNull List<Long> ids) {
        return entityManager
                .createQuery("SELECT sp from SutoPropertyDao sp where sp.id in :ids", SutoPropertyDao.class)
                .setParameter("ids", ids)
                .getResultList()
                .stream().map(propertyToDaoMapper::map).toList();
    }

    @Override
    public List<SutoProperty> findAll() {
        return entityManager.createQuery("select sp from SutoPropertyDao sp",SutoPropertyDao.class)
                .getResultList()
                .stream().map(propertyToDaoMapper::map).toList();
    }
}
