package com.deb.spl.control;

import com.deb.spl.control.utils.Pdf.TmpFileUtils;
import com.vaadin.flow.component.dependency.NpmPackage;
import com.vaadin.flow.component.page.AppShellConfigurator;
import com.vaadin.flow.component.page.Push;
import com.vaadin.flow.server.PWA;
import com.vaadin.flow.theme.Theme;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import java.util.Map;

/**
 * The entry point of the Spring Boot application.
 * <p>
 * Use the @PWA annotation make the application installable on phones, tablets
 * and some desktop browsers.
 */
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, UserDetailsServiceAutoConfiguration.class})
@EnableScheduling
@EnableAsync
@Push
@Theme(value = "myapp")
@PWA(name = "SDO-SPL-Control", shortName = "SPL-Control", offlineResources = {})
@NpmPackage(value = "line-awesome", version = "1.3.0")
@Slf4j
public class Application extends SpringBootServletInitializer implements AppShellConfigurator {
    public static void main(String[] args) {
        ApplicationContext context = SpringApplication.run(Application.class, args);
    }

    @Value("${host.url}")
    private String hostUrl;

    @Value("${ccv.rocket-status-endpoint.url}")
    private String rocketStatusEndpoint;

    @Value("${ccv.token.value}")
    private String ccvToken;

    @Value("${asku.test.test:/swagger-ui.html}")
    private String askuTestValue;

    @EventListener(ApplicationReadyEvent.class)
    public void runAfterStart() {
        TmpFileUtils.removeTmpFilesRoot();
        log.info("temporary files location : " + TmpFileUtils.getTmpFilesFolder().getAbsolutePath());

        java.util.Map<String, String> env = System.getenv();

        for (Map.Entry<String, String> entry : env.entrySet()) {
            String variableName = entry.getKey();
            String variableValue = entry.getValue();
            log.info(variableName + "=" + variableValue);
        }

        log.info("host.url " + hostUrl);
        log.info("ccv.rocket-status-endpoint.url " + rocketStatusEndpoint);
        log.info("ccv.token " + ccvToken);
        log.info("asku.test.test " + askuTestValue);
        log.info("application loaded");
    }

    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        super.onStartup(servletContext);
    }
}
