package com.deb.spl.control;

import java.util.HashMap;
import java.util.Map;

public class MaxConsecutiveRepeats {
    public static void main(String[] args) {
        String testString = "aaffbaaaafcz";
        Map<Character, Integer> output = new HashMap<>();
        output = countConsecutiveRepeats(testString, 0, testString.charAt(0), 1, output);
        System.out.println(output);

    }

    static Map<Character, Integer> countConsecutiveRepeats(String input, int nextSymbolPosition, char processedSymbol, int symbolCount, Map<Character, Integer> symbolsMap) {
        if (input.isEmpty()) {
            return new HashMap<>();
        }

        if (symbolsMap == null) {
            symbolsMap = new HashMap<>();
        }

        if (nextSymbolPosition == input.length()) {
            return symbolsMap;
        }

        char nextSymbol = input.charAt(nextSymbolPosition);
        nextSymbolPosition++;

        if (nextSymbol == processedSymbol) {
            symbolCount++;

            return countConsecutiveRepeats(input, nextSymbolPosition, nextSymbol, symbolCount, symbolsMap);
        }

        if (symbolsMap.getOrDefault(processedSymbol, 0) > 0) {
            symbolsMap.put(processedSymbol, symbolCount);
        }

        symbolCount = 0;

        return countConsecutiveRepeats(input, nextSymbolPosition, nextSymbol, symbolCount, symbolsMap);
    }


}
