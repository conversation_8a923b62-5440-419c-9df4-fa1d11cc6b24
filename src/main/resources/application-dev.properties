host.url=localhost
#ccv.host.url=http://************
ccv.host.url=http://*************
ccv.rocket-status-endpoint.url=http://************/api/asku/status/
ccv.app-settings.url=${ccv.host.url}/api/app_settings
ccv.vehicles.url=${ccv.host.url}/api/vehicles
ccv.token.request-parameter-name=Token
ccv.token.value=spl12345
plc.token.request-parameter-name=Token
plc.request.token=plc12345
plc.response.token=server67890
#plc.address=*************
plc.url=*************,0:0:0:0:0:0:0:1

spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.highlight_sql=false
spring.jpa.properties.hibernate.generate_statistics=false
logging.level.org.hibernate.SQL=error
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=ERROR
logging.level.org.hibernate.hql.internal.ast.QueryTranslatorImpl=ERROR

#swagger
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true