#spring.profiles.active=dev
server.port=${PORT:8079}
host.url=localhost
server.error.include-stacktrace=never
logging.level.org.atmosphere=error
spring.mustache.check-template-location=false
# Launch the default browser when starting the application in development mode
vaadin.launch-browser=true
#vaadin.urlMapping=/ui/
# PostgreSQL configuration.
spring.datasource.url=jdbc:postgresql://${host.url}:5532/spl_control
spring.datasource.username=postgres
spring.datasource.password=123
spring.liquibase.enabled=true
spring.jpa.hibernate.ddl-auto=validate
#spring.jpa.show-sql=true

spring.jpa.properties.hibernate.show_sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.highlight_sql=true
#hybernate statistics
spring.jpa.properties.hibernate.generate_statistics=false

logging.level.org.hibernate.SQL=info
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=trace
logging.file.path=./
logging.file.name=VehicleControlServices.log
spring.jpa.defer-datasource-initialization=false
spring.sql.init.mode=always

#swagger settings
springdoc.api-docs.path=/api-docs
springdoc.show-actuator=true
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true

# To improve the performance during development.
# For more information https://vaadin.com/docs/flow/spring/tutorial-spring-configuration.html#special-configuration-parameters
vaadin.whitelisted-packages=com.vaadin,org.vaadin,dev.hilla,com.example.application
vaadin.excludeUrls=/swagger-ui/**
home.url=/adjacent-systems/sae
asku.security.config.endpoint.pattern.permit-all=/images/**,/VAADIN/**,/,/adjacent-systems/**,/automation/**,/readiness/**,\
  /log_main_view/**,/mmhs,/api/v1/adjacent-systems/bins/**
asku.security.config.endpoint.pattern.authenticated=/api/**
asku.pdp.countbg1_time_sec=106
asku.pdp.countbg2a_time_sec=745
asku.pdp.countbg2b_time_sec=944
asku.pdp.countbg3_time_sec=861
asku.pdp.start_shift_sec=28
asku.msg.fire-command-reminder-before-scheduled-sec=1888

basu.msg.rocket-temperature.refresh-rate-sec=2
basu-temeparature.warning.hi-hi=50
basu-temeparature.warning.hi=45
#logged url
allowed.urls.for.logging=/msu,/bins,/api,/users
#change to 60 after test
bins.connection-time-out-sec=30
bins.sleep-time-on-error-sec=60
bins.data-source-endpoint.url=http://${host.url}:8081/bins
time.utc-zone-id=+3
time.from-bins=false
plc.connection-time-out-sec=8
plc.connection-error-retries=3
plc.request.token=plc12345
plc.response.token=server67890
plc.url=localhost
#CCV properties
ccv.host.url=http://localhost
ccv.rocket-status-endpoint.url=http://localhost/api/asku/status/
ccv.app-settings.url=${ccv.host.url}/api/app_settings
ccv.vehicles.url=${ccv.host.url}/api/vehicles
ccv.token.request-parameter-name=Token
ccv.token.value=spl12345
spl.default-spl-id=02d9dd7e-8cda-40f1-b068-b04a23841097
spl.plate-number=DP 0101 UA
spl.unit.title=spl101
#determines period of command is validity between generation and attempt to send the get command
sae.command.command-validity-time-sec=30
#use "," as spliterator (command1,command2,command3)
#sae.command.pdp.automation=TurnOffFeeder1NPPA
sae.command.pdp.automation=TurnOffFeeder1NPPA
ppo.command.command-validity-time-sec=30
suto.command.command-validity-time-sec=30
nppa.command.command-validity-time-sec=30
nppa.command.commit-awaiting-time-sec=120

nppa.command.launch.control=Otr1FromBg1Launch,Otr1FromBg2aLaunch,Otr1FromBg2bLaunch,Otr1FromBg3Launch,\
  Otr2FromBg3Launch,Otr2FromBg2aLaunch,Otr2FromBg2bLaunch,Otr2FromBg1Launch,Otr1Otr2FromBg3Launch,\
  Otr1Otr2FromBg2aLaunch,Otr1Otr2FromBg2bLaunch,Otr1Otr2FromBg1Launch
nppa.command.launch.control.exclude=Otr1LaunchCancell,Otr2LaunchCancell,PPiPWithBg2bContinuation,\
  Otr1AutomaticLaunchCancelAcknowledgment,Otr2AutomaticLaunchCancelAcknowledgment
nppa.command.pdp.automation=AutoNppaEbasu1Ebasu2TestMode,AutoNppaEbasu1TestMode,AutoNppaEbasu2TestMode,\
  AutoOtr1AfterReloadingTestMode,AutoOtr1TestMode,AutoOtr2AfterReloadingTestMode,AutoOtr2TestMode,CancelAutoTestMode
nppa.commands.enabled-in-ncok-not-connected =Otr2FromBg3Launch,Otr2FromBg2bLaunch,\
  Otr1Otr2FromBg3Launch,Otr1Otr2FromBg2bLaunch,Otr1FromBg3Launch,\
  Otr1FromBg2bLaunch,Bg4toBg3Toggle,Bg3toBg2bToggle,Bg3toBg2aToggle,\
  Bg3ToBg1SwitchWithOtr2SwOn,Bg3ToBg1SwitchWithOtr1SwOn,Bg3ToBg1SwitchWithOtr1Otr2SwOn

view.ncok-command-status-grid.enale=false

msu.heating-command-delay-ms=5000
msu.connection-time-out-sec=30
msu.sleep-time-on-error-sec=60
msu.data-source-endpoint.url=http://${host.url}:8081

mmhs.url=http://${host.url}:3002
#greater than 10
ccv.request.time-limit.sec=40
ccv.refresh-delay.sec=30
ccv.connection-lost-delay.sec=120
#used to find vehicle of CCV type in vehicle resources []
ccv.json.property.type.name=CCM
ccv.security.config.endpoint.pattern.permit-all=/api/v1/asku/,/api/v1/asku/tlc/open_keys/**,/api/v1/asku/initial_data/**,\
  /api/v1/asku/fire_order/**,/api/v1/asku/state,/api/v1/asku/state/,/api/v1/asku/reference_points/,/api/v1/asku/reference_points
ccv.security.config.endpoint.pattern.permit-all.post=/api/v1/asku/initial_data/,/api/v1/asku/fire_order/**,
#NSD (tritel)
nsd.token=12345
nsd.url=/users/create-api,/users/create-admin-api,/users/test
#Set to 0 or a negative number to disable the notification auto-closing. default 15
events.messages.commit.display-time-sec=15
#Set to 0 or a negative number to disable the notification auto-closing. default 50
events.messages.warning.display-time-sec=50
#Set to 0 or a negative number to disable the notification auto-closing. default 0
events.messages.error.display-time-sec=0
#Set to 0 or a negative number to disable the notification auto-closing. default 0
events.messages.default.display-time-sec=25
log.adjacent-system-view.enable=false
#report
report.enabled=false
report.filename.prefix=spl_control
report.filename.type.http=http
report.filename.type.commands=commands
report.filename.type.system_states=systems_states
report.filename.timestamp=false
report.filename.delimeter=_
#enables pageNumber printing
report.header_footer.enabled=true
#
report.pageNumber.prefix=Сторінка
#page number x pos 0-595, default 520.0f
report.pageNumber.x=516.0f
#page number y pos 0-830, default 10.0f
report.pageNumber.y=10.0f
report.title-page.show=true
report.title-page.settings.show=true
report.title-page.documentDescription=This document describes something which is very important. \n Replace this line in application.properties or docker-compose
report.rows.header.captions=id,Система,Оновлено,Ознака,Дані
#report.rows.withs=120f,120f,180f,120f,800f
report.rows.withs=9f,9f,13f,11f,58f
report.ignore-for-export=UNDEFINED,UNCLASSIFIED,NSD,PLC,TPC,CCV
