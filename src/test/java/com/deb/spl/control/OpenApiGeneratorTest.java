package com.deb.spl.control;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.web.context.WebApplicationContext;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "logging.level.org.springframework=WARN",
    "logging.level.com.deb.spl=WARN",
    "server.port=0"
})
public class OpenApiGeneratorTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    public void generateOpenApiSpec() throws Exception {
        MockMvc mockMvc = webAppContextSetup(webApplicationContext).build();
        
        try {
            // Get OpenAPI JSON from the endpoint
            MvcResult result = mockMvc.perform(get("/api-docs"))
                .andExpect(status().isOk())
                .andReturn();
            
            String openApiJson = result.getResponse().getContentAsString();
            
            // Convert JSON to YAML using Jackson
            ObjectMapper jsonMapper = new ObjectMapper();
            ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());
            
            Object jsonObject = jsonMapper.readValue(openApiJson, Object.class);
            String yamlContent = yamlMapper.writeValueAsString(jsonObject);
            
            // Ensure directories exist
            File targetDir = new File("target/generated-sources/openapi");
            targetDir.mkdirs();
            
            File dockerDir = new File("docker/spl_control");
            dockerDir.mkdirs();
            
            // Write files
            writeToFile("target/generated-sources/openapi/openapi.yml", yamlContent);
            writeToFile("target/generated-sources/openapi/openapi.json", openApiJson);
            writeToFile("docker/spl_control/openapi.yml", yamlContent);
            writeToFile("docker/spl_control/openapi.json", openApiJson);
            writeToFile("openapi.yml", yamlContent);
            
            System.out.println("✓ OpenAPI files generated successfully!");
            System.out.println("  - target/generated-sources/openapi/openapi.yml");
            System.out.println("  - target/generated-sources/openapi/openapi.json");
            System.out.println("  - docker/spl_control/openapi.yml");
            System.out.println("  - docker/spl_control/openapi.json");
            System.out.println("  - openapi.yml");
            
        } catch (Exception e) {
            System.err.println("Error generating OpenAPI spec: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    private void writeToFile(String filename, String content) throws IOException {
        try (FileWriter writer = new FileWriter(filename)) {
            writer.write(content);
        }
    }
}
