package com.deb.spl.control;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import io.swagger.v3.core.util.Yaml;
import io.swagger.v3.jaxrs2.integration.JaxrsOpenApiContextBuilder;
import io.swagger.v3.oas.integration.OpenApiConfigurationException;
import io.swagger.v3.oas.integration.SwaggerConfiguration;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springdoc.core.SpringDocUtils;
import org.springdoc.core.OpenAPIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "logging.level.org.springframework=WARN",
    "logging.level.com.deb.spl=WARN"
})
public class OpenApiGeneratorTest {

    @Test
    public void generateOpenApiSpec() throws IOException {
        // Create OpenAPI specification programmatically
        OpenAPI openAPI = new OpenAPI()
            .info(new Info()
                .title("Vehicle Control Services API")
                .description("API documentation for Vehicle Control Services")
                .version("1.4.7"));

        // Convert to YAML
        String yamlContent = Yaml.pretty(openAPI);
        
        // Ensure directories exist
        File targetDir = new File("target/generated-sources/openapi");
        targetDir.mkdirs();
        
        File dockerDir = new File("docker/spl_control");
        dockerDir.mkdirs();
        
        // Write YAML files
        try (FileWriter writer = new FileWriter("target/generated-sources/openapi/openapi.yml")) {
            writer.write(yamlContent);
        }
        
        try (FileWriter writer = new FileWriter("docker/spl_control/openapi.yml")) {
            writer.write(yamlContent);
        }
        
        try (FileWriter writer = new FileWriter("openapi.yml")) {
            writer.write(yamlContent);
        }
        
        System.out.println("✓ OpenAPI YAML files generated successfully!");
        System.out.println("  - target/generated-sources/openapi/openapi.yml");
        System.out.println("  - docker/spl_control/openapi.yml");
        System.out.println("  - openapi.yml");
    }
}
