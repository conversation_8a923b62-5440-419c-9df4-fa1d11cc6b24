package com.deb.spl.control;

import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class SimpleOpenApiGeneratorTest {

    @Test
    public void generateBasicOpenApiSpec() throws IOException {
        String openApiYaml = createBasicOpenApiYaml();
        
        // Ensure directories exist
        new File("target/generated-sources/openapi").mkdirs();
        new File("docker/spl_control").mkdirs();
        
        // Write YAML files
        writeToFile("target/generated-sources/openapi/openapi.yml", openApiYaml);
        writeToFile("docker/spl_control/openapi.yml", openApiYaml);
        writeToFile("openapi.yml", openApiYaml);
        
        System.out.println("✓ OpenAPI YAML files generated successfully!");
        System.out.println("  - target/generated-sources/openapi/openapi.yml");
        System.out.println("  - docker/spl_control/openapi.yml");
        System.out.println("  - openapi.yml");
    }
    
    private String createBasicOpenApiYaml() {
        return "openapi: 3.0.1\n" +
            "info:\n" +
            "  title: Vehicle Control Services API\n" +
            "  description: API documentation for Vehicle Control Services\n" +
            "  version: 1.4.7\n" +
            "servers:\n" +
            "  - url: http://localhost:8079\n" +
            "    description: Development server\n" +
            "paths:\n" +
            "  /api/v1/asku:\n" +
            "    get:\n" +
            "      tags:\n" +
            "        - asku-controller\n" +
            "      summary: Get ASKU information\n" +
            "      operationId: getAsku\n" +
            "      responses:\n" +
            "        '200':\n" +
            "          description: Successful response\n" +
            "          content:\n" +
            "            application/json:\n" +
            "              schema:\n" +
            "                $ref: '#/components/schemas/AskuDto'\n" +
            "        '400':\n" +
            "          description: Bad Request\n" +
            "  /api/v1/asku/rocket:\n" +
            "    get:\n" +
            "      tags:\n" +
            "        - asku-controller\n" +
            "      summary: Get rocket information\n" +
            "      parameters:\n" +
            "        - name: isLeft\n" +
            "          in: query\n" +
            "          required: false\n" +
            "          schema:\n" +
            "            type: boolean\n" +
            "            default: true\n" +
            "      responses:\n" +
            "        '200':\n" +
            "          description: Successful response\n" +
            "          content:\n" +
            "            application/json:\n" +
            "              schema:\n" +
            "                $ref: '#/components/schemas/RocketDto'\n" +
            "    post:\n" +
            "      tags:\n" +
            "        - asku-controller\n" +
            "      summary: Set rocket information\n" +
            "      parameters:\n" +
            "        - name: isLeft\n" +
            "          in: query\n" +
            "          required: false\n" +
            "          schema:\n" +
            "            type: boolean\n" +
            "            default: true\n" +
            "      requestBody:\n" +
            "        required: true\n" +
            "        content:\n" +
            "          application/json:\n" +
            "            schema:\n" +
            "              $ref: '#/components/schemas/RocketDto'\n" +
            "      responses:\n" +
            "        '200':\n" +
            "          description: Successful response\n" +
            "components:\n" +
            "  schemas:\n" +
            "    AskuDto:\n" +
            "      type: object\n" +
            "      properties:\n" +
            "        plateNumber:\n" +
            "          type: string\n" +
            "        unitTitle:\n" +
            "          type: string\n" +
            "        splReadiness:\n" +
            "          type: string\n" +
            "          enum:\n" +
            "            - 'БГ № 1'\n" +
            "            - 'БГ № 2а'\n" +
            "            - 'БГ № 2'\n" +
            "            - 'БГ № 3'\n" +
            "            - 'ТГ № 4'\n" +
            "    RocketDto:\n" +
            "      type: object\n" +
            "      required:\n" +
            "        - technicalCondition\n" +
            "      properties:\n" +
            "        technicalCondition:\n" +
            "          type: boolean\n" +
            "        sensorTemperature:\n" +
            "          type: number\n" +
            "          format: double\n" +
            "        formData:\n" +
            "          $ref: '#/components/schemas/RocketFormDataDto'\n" +
            "    RocketFormDataDto:\n" +
            "      type: object\n" +
            "      required:\n" +
            "        - plantMissile\n" +
            "        - warhead\n" +
            "        - gsnType\n" +
            "        - alpType\n" +
            "        - isTelemetryIntegrated\n" +
            "        - purposeType\n" +
            "      properties:\n" +
            "        plantMissile:\n" +
            "          type: string\n" +
            "        warhead:\n" +
            "          type: string\n" +
            "          enum: [MFBCH, CBCH, UNDEFINED]\n" +
            "        gsnType:\n" +
            "          type: string\n" +
            "          enum: [NO_GSN, K_GSN, OE_GSN, UNDEFINED]\n" +
            "        alpType:\n" +
            "          type: string\n" +
            "          enum: [NO_ALP, FOUR_ALP, TWO_ALP, UNDEFINED]\n" +
            "        isTelemetryIntegrated:\n" +
            "          type: boolean\n" +
            "        purposeType:\n" +
            "          type: string\n" +
            "          enum: [COMBAT, TEM, UNDEFINED]\n";
    }
    
    private void writeToFile(String filename, String content) throws IOException {
        try (FileWriter writer = new FileWriter(filename)) {
            writer.write(content);
        }
    }
}
